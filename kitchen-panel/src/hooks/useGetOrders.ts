import { useQuery } from "@tanstack/react-query";
import { Order } from "../models/order.model";
import axios from "axios";

const fetchOrders = async (): Promise<Order[]> => {
    const response = await axios.get('/orders');
    const sortedOrders = response.data.sort((a: Order, b: Order) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
    );
    return sortedOrders;
};

export const useGetOrders = () => {
    const { data: orders = [], refetch: refreshOrders, isLoading, error } = useQuery({
        queryKey: ['orders'],
        queryFn: fetchOrders,
        refetchInterval: 5000, // Auto-refresh every 5 seconds for kitchen real-time updates
        refetchIntervalInBackground: true, // Continue refetching when window is not focused
    });

    return {
        orders,
        refreshOrders,
        isLoading,
        error
    };
};
