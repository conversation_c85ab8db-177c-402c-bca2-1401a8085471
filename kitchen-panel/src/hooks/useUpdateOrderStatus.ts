import { useMutation, useQueryClient } from '@tanstack/react-query';
import axios from 'axios';

interface UpdateOrderStatusParams {
    localId: string;
    orderStatus: string;
}

const updateOrderStatusApi = async ({ localId, orderStatus }: UpdateOrderStatusParams) => {
    const response = await axios.patch(`/orders/${localId}`, {
        orderStatus
    });
    return response.data;
};

export const useUpdateOrderStatus = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation({
        mutationFn: updateOrderStatusApi,
        onSuccess: () => {
            // Invalidate and refetch orders after successful update
            queryClient.invalidateQueries({ queryKey: ['orders'] });
        },
        onError: (error: unknown) => {
            console.error('Error updating order status:', error);
        },
    });

    const updateOrderStatus = (localId: string, orderStatus: string) => {
        return mutation.mutate({ localId, orderStatus });
    };

    return {
        updateOrderStatus,
        isUpdating: mutation.isPending,
        error: mutation.error
    };
};
