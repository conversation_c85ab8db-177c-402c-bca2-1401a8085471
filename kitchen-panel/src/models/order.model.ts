export type OrderStatus = 'created' | 'in_progress' | 'ready_for_pickup' | 'completed' | 'cancelled';

export interface OrderItem {
  lineId: string;
  itemCode: string;
  itemName: string;
  quantity: number;
  unitPrice: string;
  currency: string;
  customizations?: Record<string, unknown>;
  lineTotal: string;
}

export interface Payment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  method?: {
    name: string;
  };
}

export interface Order {
  _id: string;
  localId: string;
  displayId: number;
  customer: string;
  totalAmount: number;
  currency: string;
  orderStatus: string;
  syncStatus: string;
  posProfile: string;
  tableNumber?: string;
  status?: OrderStatus;
  items: OrderItem[];
  payments?: Payment[];
  createdAt: string;
  updatedAt: string;
  estimatedTime?: string;
}
