import { useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useGetOrders } from '../hooks/useGetOrders';
import { useUpdateOrderStatus } from '../hooks/useUpdateOrderStatus';
import { Order } from '../models/order.model';
import { OrderEvents } from '../enum/order-websocket-event-types';
import { Button } from '../components/ui/button';
import { Card, CardHeader, CardContent, CardFooter } from '../components/ui/card';

// Utility functions for order card
const getTimeSinceCreation = (createdAt: string): number => {
  const now = new Date();
  const created = new Date(createdAt);
  return Math.floor((now.getTime() - created.getTime()) / (1000 * 60)); // minutes
};

const getTimeIndicator = (minutes: number): { borderColor: string; bgColor: string; dotColor: string } => {
  if (minutes <= 4) {
    return { borderColor: 'border-blue-200', bgColor: 'bg-blue-50', dotColor: 'bg-blue-500' };
  } else if (minutes <= 8) {
    return { borderColor: 'border-yellow-200', bgColor: 'bg-yellow-50', dotColor: 'bg-yellow-500' };
  } else {
    return { borderColor: 'border-red-200', bgColor: 'bg-red-50', dotColor: 'bg-red-500' };
  }
};

const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  const time = date.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
  const dateFormatted = date.toLocaleDateString('en-GB', { day: '2-digit', month: '2-digit', year: '2-digit' });
  return `${time} ${dateFormatted}`;
};

const getOrderType = (order: Order): string => {
  if (order.tableNumber) {
    return `Dine-In (Table ${order.tableNumber})`;
  }
  // You can extend this logic based on other order properties
  return 'Pickup'; // Default fallback
};

export default function OrdersPage() {
  const { orders: dbOrders, refreshOrders } = useGetOrders();
  const { updateOrderStatus, isUpdating } = useUpdateOrderStatus();
  const [, setSocket] = useState<Socket | null>(null);
  const [visibleSections, setVisibleSections] = useState<{
    inPreparation: boolean;
    ready: boolean;
  }>({
    inPreparation: true,
    ready: true,
  });

  // Filter orders by status
  const inProgressOrders = dbOrders.filter(order => order.orderStatus === 'Paid');
  const readyOrders = dbOrders.filter(order => order.orderStatus === 'Ready');

  useEffect(() => {
    const socket = io(`${import.meta.env.VITE_API_URL}/orders`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    socket.on(OrderEvents.ORDER_CREATED, () => {
      refreshOrders();
    });

    socket.on(OrderEvents.ORDER_UPDATED, () => {
      refreshOrders();
    });

    socket.on(OrderEvents.ORDER_DELETED, () => {
      refreshOrders();
    });

    setSocket(socket);

    return () => {
      socket.disconnect();
    };
  }, [refreshOrders]);

  const handleMoveToState = useCallback((order: Order, nextState: string) => {
    updateOrderStatus(order.localId, nextState);
  }, [updateOrderStatus]);

  const toggleSection = (section: 'inPreparation' | 'ready') => {
    setVisibleSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Determine layout based on visible sections
  const showBothSections = visibleSections.inPreparation && visibleSections.ready;
  const showOnlyInPreparation = visibleSections.inPreparation && !visibleSections.ready;
  const showOnlyReady = !visibleSections.inPreparation && visibleSections.ready;
  const showNeitherSection = !visibleSections.inPreparation && !visibleSections.ready;

  const OrderCard = ({ order, buttonType }: { order: Order; buttonType?: 'ready' | 'inprogress' | 'none' }) => {
    const minutesSinceCreation = getTimeSinceCreation(order.createdAt);
    const timeIndicator = getTimeIndicator(minutesSinceCreation);
    const formattedDateTime = formatDateTime(order.createdAt);
    const orderType = getOrderType(order);

    return (
      <Card className={`w-80 ${timeIndicator.bgColor} border-2 ${timeIndicator.borderColor} rounded-xl shadow-md p-0 m-2`}>
        {/* Header Section */}
        <CardHeader className="pb-3 px-6 pt-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-xl font-bold text-gray-800">ORDER #{order.displayId}</h3>
            <div className={`w-4 h-4 rounded-full ${timeIndicator.dotColor} shadow-sm`}></div>
          </div>
          <div className="flex justify-between items-center text-sm text-gray-600">
            <span className="font-medium">{formattedDateTime}</span>
            <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-semibold">
              Approved
            </span>
          </div>
        </CardHeader>

        {/* Body Section */}
        <CardContent className="px-6 py-2">
          <div className="space-y-3">
            {order.items?.map((item, index) => (
              <div key={index} className="space-y-1">
                <div className="flex gap-2">
                  <span className="font-bold text-gray-800">{item.quantity}x</span>
                  <span className="font-bold text-gray-800">{item.itemName}</span>
                </div>
                {item.customizations && Object.keys(item.customizations).length > 0 && (
                  <div className="ml-6 text-sm text-gray-500 italic">
                    {/* Display customizations if available */}
                    {JSON.stringify(item.customizations)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>

        {/* Footer Section */}
        <CardFooter className="px-6 pt-4 pb-6">
          <div className="flex justify-between items-center w-full">
            <span className="text-sm font-semibold text-gray-700 bg-white px-3 py-1 rounded-full shadow-sm">
              {orderType}
            </span>

            {buttonType === 'ready' && (
              <Button
                onClick={() => handleMoveToState(order, 'Ready')}
                disabled={isUpdating}
                className="bg-orange-500 hover:bg-orange-600 text-white border-0 px-6 py-2 rounded-full shadow-md font-semibold transition-all duration-200 hover:shadow-lg"
              >
                Ready
              </Button>
            )}

            {buttonType === 'inprogress' && (
              <Button
                onClick={() => handleMoveToState(order, 'Paid')}
                disabled={isUpdating}
                className="bg-green-500 hover:bg-green-600 text-white border-0 px-6 py-2 rounded-full shadow-md font-semibold transition-all duration-200 hover:shadow-lg"
              >
                In Preparation
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-white p-4">
      <div className="mb-4 text-center">
        <h1 className="text-2xl font-bold">KITCHEN DISPLAY</h1>

        {/* Toggle Filter Controls */}
        <div className="mt-4 flex justify-center gap-4">
          <Button
            onClick={() => toggleSection('inPreparation')}
            className={`w-48 px-4 py-2 border border-black text-sm ${visibleSections.inPreparation
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-white text-black hover:bg-orange-100'
              }`}
          >
            In Preparation ({inProgressOrders.length})
          </Button>
          <Button
            onClick={() => toggleSection('ready')}
            className={`w-48 px-4 py-2 border border-black text-sm ${visibleSections.ready
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-white text-black hover:bg-green-100'
              }`}
          >
            Ready ({readyOrders.length})
          </Button>
        </div>
      </div>

      {/* Conditional Layout Based on Visible Sections */}
      {showNeitherSection ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Select a section to view orders</p>
        </div>
      ) : showBothSections ? (
        /* Both sections - Split view with black line */
        <div className="grid grid-cols-2 gap-0">
          {/* Left Section: In Preparation Orders */}
          <div className="p-4">
            <h2 className="text-xl font-bold mb-4 text-center">In Preparation ({inProgressOrders.length})</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
              {inProgressOrders.map((order) => (
                <OrderCard key={order.localId} order={order} buttonType="ready" />
              ))}
            </div>
          </div>

          {/* Black line between sections */}
          <div className="border-l border-black">
            {/* Right Section: Ready Orders */}
            <div className="p-4">
              <h2 className="text-xl font-bold mb-4 text-center">READY ({readyOrders.length})</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
                {readyOrders.map((order) => (
                  <OrderCard key={order.localId} order={order} buttonType="inprogress" />
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : showOnlyInPreparation ? (
        /* Only In Preparation - Full screen */
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4 text-center">In Preparation ({inProgressOrders.length})</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-2">
            {inProgressOrders.map((order) => (
              <OrderCard key={order.localId} order={order} buttonType="ready" />
            ))}
          </div>
        </div>
      ) : showOnlyReady ? (
        /* Only Ready - Full screen */
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4 text-center">READY ({readyOrders.length})</h2>
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-2">
            {readyOrders.map((order) => (
              <OrderCard key={order.localId} order={order} buttonType="inprogress" />
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
}