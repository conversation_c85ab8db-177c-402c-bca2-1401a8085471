import { useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useGetOrders } from '../hooks/useGetOrders';
import { useUpdateOrderStatus } from '../hooks/useUpdateOrderStatus';
import { Order } from '../models/order.model';
import { OrderEvents } from '../enum/order-websocket-event-types';
import { Button } from '../components/ui/button';

export default function OrdersPage() {
  const { orders: dbOrders, refreshOrders } = useGetOrders();
  const { updateOrderStatus, isUpdating } = useUpdateOrderStatus();
  const [, setSocket] = useState<Socket | null>(null);
  const [visibleSections, setVisibleSections] = useState<{
    inPreparation: boolean;
    ready: boolean;
  }>({
    inPreparation: true,
    ready: true,
  });

  // Filter orders by status
  const inProgressOrders = dbOrders.filter(order => order.orderStatus === 'Paid');
  const readyOrders = dbOrders.filter(order => order.orderStatus === 'Ready');

  useEffect(() => {
    const socket = io(`${import.meta.env.VITE_API_URL}/orders`, {
      transports: ['websocket'],
      autoConnect: true,
    });

    socket.on(OrderEvents.ORDER_CREATED, () => {
      refreshOrders();
    });

    socket.on(OrderEvents.ORDER_UPDATED, () => {
      refreshOrders();
    });

    socket.on(OrderEvents.ORDER_DELETED, () => {
      refreshOrders();
    });

    setSocket(socket);

    return () => {
      socket.disconnect();
    };
  }, [refreshOrders]);

  const handleMoveToState = useCallback((order: Order, nextState: string) => {
    updateOrderStatus(order.localId, nextState);
  }, [updateOrderStatus]);

  const toggleSection = (section: 'inPreparation' | 'ready') => {
    setVisibleSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Determine layout based on visible sections
  const showBothSections = visibleSections.inPreparation && visibleSections.ready;
  const showOnlyInPreparation = visibleSections.inPreparation && !visibleSections.ready;
  const showOnlyReady = !visibleSections.inPreparation && visibleSections.ready;
  const showNeitherSection = !visibleSections.inPreparation && !visibleSections.ready;

  const OrderCard = ({ order, buttonType }: { order: Order; buttonType?: 'ready' | 'inprogress' | 'none' }) => (
    <div className="bg-white p-4 border border-black">
      <div className="mb-2">
        <h3 className="text-lg font-bold">ORDER #{order.displayId}</h3>
        <p>{order.customer}</p>
      </div>

      <div className="mb-2">
        {order.items?.map((item, index) => (
          <div key={index} className="flex gap-2">
            <span>{item.quantity}x</span>
            <span>{item.itemName}</span>
          </div>
        ))}
      </div>

      {buttonType === 'ready' && (
        <button
          onClick={() => handleMoveToState(order, 'Ready')}
          disabled={isUpdating}
          className="bg-white border border-black px-4 py-2"
        >
          Ready
        </button>
      )}

      {buttonType === 'inprogress' && (
        <button
          onClick={() => handleMoveToState(order, 'Paid')}
          disabled={isUpdating}
          className="bg-white border border-black px-4 py-2"
        >
          In Preparation
        </button>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-white p-4">
      <div className="mb-4 text-center">
        <h1 className="text-2xl font-bold">KITCHEN DISPLAY</h1>

        {/* Toggle Filter Controls */}
        <div className="mt-4 flex justify-center gap-4">
          <Button
            onClick={() => toggleSection('inPreparation')}
            className={`w-48 px-4 py-2 border border-black text-sm ${visibleSections.inPreparation
              ? 'bg-orange-500 text-white hover:bg-orange-600'
              : 'bg-white text-black hover:bg-orange-100'
              }`}
          >
            In Preparation ({inProgressOrders.length})
          </Button>
          <Button
            onClick={() => toggleSection('ready')}
            className={`w-48 px-4 py-2 border border-black text-sm ${visibleSections.ready
              ? 'bg-green-500 text-white hover:bg-green-600'
              : 'bg-white text-black hover:bg-green-100'
              }`}
          >
            Ready ({readyOrders.length})
          </Button>
        </div>
      </div>

      {/* Conditional Layout Based on Visible Sections */}
      {showNeitherSection ? (
        <div className="text-center py-8">
          <p className="text-gray-500">Select a section to view orders</p>
        </div>
      ) : showBothSections ? (
        /* Both sections - Split view with black line */
        <div className="grid grid-cols-2 gap-0">
          {/* Left Section: In Preparation Orders */}
          <div className="p-4">
            <h2 className="text-xl font-bold mb-4 text-center">In Preparation ({inProgressOrders.length})</h2>
            <div className="flex flex-wrap gap-2">
              {inProgressOrders.map((order) => (
                <OrderCard key={order.localId} order={order} buttonType="ready" />
              ))}
            </div>
          </div>

          {/* Black line between sections */}
          <div className="border-l border-black">
            {/* Right Section: Ready Orders */}
            <div className="p-4">
              <h2 className="text-xl font-bold mb-4 text-center">READY ({readyOrders.length})</h2>
              <div className="flex flex-wrap gap-2">
                {readyOrders.map((order) => (
                  <OrderCard key={order.localId} order={order} buttonType="inprogress" />
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : showOnlyInPreparation ? (
        /* Only In Preparation - Full screen */
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4 text-center">In Preparation ({inProgressOrders.length})</h2>
          <div className="flex flex-wrap gap-2">
            {inProgressOrders.map((order) => (
              <OrderCard key={order.localId} order={order} buttonType="ready" />
            ))}
          </div>
        </div>
      ) : showOnlyReady ? (
        /* Only Ready - Full screen */
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4 text-center">READY ({readyOrders.length})</h2>
          <div className="flex flex-wrap gap-2">
            {readyOrders.map((order) => (
              <OrderCard key={order.localId} order={order} buttonType="inprogress" />
            ))}
          </div>
        </div>
      ) : null}
    </div>
  );
}