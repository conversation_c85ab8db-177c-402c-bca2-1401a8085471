# Kitchen Display System - Architecture Documentation

## 1. System Overview

The Kitchen Display System (KDS) is a real-time web application built with a React frontend and NestJS backend, utilizing WebSocket connections for live order updates and RESTful APIs for order management.

## 2. Architecture Diagram

```
┌─────────────────┐    WebSocket    ┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│   Kitchen       │◄──────────────►│   NestJS API    │◄──────────────►│   PostgreSQL    │
│   Panel         │                 │   Server        │                 │   Database      │
│   (React)       │    HTTP/REST    │   (Backend)     │                 │                 │
│                 │◄──────────────►│                 │                 │                 │
└─────────────────┘                 └─────────────────┘                 └─────────────────┘
```

## 3. Technology Stack

### 3.1 Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **WebSocket**: Socket.IO Client
- **State Management**: React Hooks (useState, useEffect, useCallback)

### 3.2 Backend
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with TypeORM
- **Real-time**: Socket.IO
- **Authentication**: API Key-based authentication
- **Event System**: NestJS EventEmitter

### 3.3 Infrastructure
- **Containerization**: Docker & Docker Compose
- **Development**: Hot reload with volume mounting
- **Database**: PostgreSQL container

## 4. System Components

### 4.1 Frontend Components

#### OrdersPage Component
- **Purpose**: Main kitchen display interface
- **Responsibilities**:
  - Display orders in two-section layout (Paid/Ready)
  - Handle WebSocket connections for real-time updates
  - Manage order status transitions
  - Render order cards with minimal styling

#### Custom Hooks
- **useGetOrders**: Fetches orders from API with refresh capability
- **useUpdateOrderStatus**: Handles order status updates via PATCH requests

#### OrderCard Component
- **Purpose**: Individual order display
- **Features**: Order details, status-specific buttons, clean minimal design

### 4.2 Backend Components

#### Order Module
- **Controller**: REST endpoints for order management
- **Service**: Business logic for order operations
- **Entity**: Order data model with relationships
- **DTOs**: Data transfer objects for API validation

#### WebSocket Gateway
- **Purpose**: Real-time communication
- **Events**: ORDER_CREATED, ORDER_UPDATED, ORDER_DELETED
- **Namespace**: `/orders` for kitchen-specific events

## 5. Data Flow

### 5.1 Order Status Update Flow
```
1. Kitchen Staff clicks "Ready" button
2. Frontend calls PATCH /api/v1/orders/:id/status
3. Backend validates request and updates database
4. Backend emits ORDER_UPDATED event via WebSocket
5. All connected clients receive update and refresh order list
6. UI automatically updates to reflect new status
```

### 5.2 Real-time Order Creation Flow
```
1. New order created in system (via POS/external system)
2. Backend emits ORDER_CREATED event
3. Kitchen display receives WebSocket event
4. Frontend automatically refreshes order list
5. New order appears in appropriate section
```

## 6. Database Schema

### 6.1 Order Entity
```typescript
@Entity('orders')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  localId: string;

  @Column()
  displayId: string;

  @Column()
  customer: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalAmount: number;

  @Column()
  currency: string;

  @Column({ type: 'enum', enum: OrderStatus, default: OrderStatus.PAID })
  orderStatus: OrderStatus;

  @OneToMany(() => OrderItem, item => item.order)
  items: OrderItem[];

  @OneToMany(() => Payment, payment => payment.order)
  payments: Payment[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### 6.2 OrderStatus Enum
```typescript
export enum OrderStatus {
  PAID = 'Paid',
  IN_PREPARATION = 'In Preparation',
  READY = 'Ready',
  COMPLETED = 'Completed',
  CANCELLED = 'Cancelled'
}
```

## 7. API Endpoints

### 7.1 Order Management
- **GET /api/v1/orders**: Retrieve all orders with relations
- **PATCH /api/v1/orders/:id/status**: Update order status
- **Authentication**: Bearer token via ApiKeyGuard

### 7.2 WebSocket Events
- **Namespace**: `/orders`
- **Events**:
  - `ORDER_CREATED`: New order notification
  - `ORDER_UPDATED`: Order status change notification
  - `ORDER_DELETED`: Order removal notification

## 8. Security Considerations

### 8.1 Authentication
- API Key-based authentication for all HTTP requests
- Environment variable configuration for API keys
- Bearer token format: `Authorization: Bearer <API_KEY>`

### 8.2 Data Validation
- DTO validation for all API inputs
- TypeScript type safety throughout application
- Database constraints and relationships

## 9. Performance Optimizations

### 9.1 Frontend
- React useCallback for function memoization
- Efficient WebSocket event handling
- Minimal re-renders with proper dependency arrays

### 9.2 Backend
- Database relations loaded efficiently
- Event-driven architecture for real-time updates
- Connection pooling for database access

## 10. Deployment Architecture

### 10.1 Development Environment
```yaml
services:
  kitchen-panel:
    build: ./kitchen-panel
    ports: ["8083:5173"]
    volumes: ["./kitchen-panel:/app"]
    
  api:
    build: ./api
    ports: ["3000:3000"]
    environment:
      - DATABASE_URL=postgresql://...
      
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=saba_db
```

### 10.2 Production Considerations
- Load balancing for multiple kitchen displays
- Database connection pooling
- WebSocket scaling with Redis adapter
- SSL/TLS termination
- Health checks and monitoring

## 11. Error Handling

### 11.1 Frontend
- Axios error handling with user feedback
- WebSocket reconnection logic
- Graceful degradation when API unavailable

### 11.2 Backend
- Global exception filters
- Validation error responses
- Database transaction rollback
- WebSocket connection management

## 12. Monitoring and Logging

### 12.1 Application Metrics
- Order processing times
- WebSocket connection counts
- API response times
- Error rates and types

### 12.2 Business Metrics
- Orders per hour
- Average preparation time
- Status transition patterns
- Kitchen efficiency metrics
