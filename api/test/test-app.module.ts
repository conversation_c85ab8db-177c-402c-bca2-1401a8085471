import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { Company } from '../src/modules/company/entity/company.entity';
import { CustomerModule } from '../src/modules/customer/customer.module';
import { Customer } from '../src/modules/customer/entity/customer.entity';
import { ItemGroup } from '../src/modules/item-group/entity/item-group.entity';
import { ItemGroupModule } from '../src/modules/item-group/item-group.module';
import { Item } from '../src/modules/item/entity/item.entity';
import { ItemModule } from '../src/modules/item/item.module';
import { ModeOfPayment } from '../src/modules/mode-of-payment/entity/mode-of-payment.entity';
import { ModeOfPaymentModule } from '../src/modules/mode-of-payment/mode-of-payment.module';
import { CompanyModule } from '../src/modules/company/company.module';
import { OrderItem } from '../src/modules/order/entity/order-item.entity';
import { Order } from '../src/modules/order/entity/order.entity';
import { Payment } from '../src/modules/order/entity/payment.entity';
import { OrderModule } from '../src/modules/order/order.module';
import { PosProfile } from '../src/modules/pos-profile/entity/pos-profile.entity';
import { PosProfileModule } from '../src/modules/pos-profile/pos-profile.module';
import { ItemPrice } from '../src/modules/pricing/entity/item-price.entity';
import { PriceList } from '../src/modules/pricing/entity/price-list.entity';
import { PricingModule } from '../src/modules/pricing/pricing.module';
import { TestDatabase } from './test-database';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    TypeOrmModule.forRootAsync({
      useFactory: (): TypeOrmModuleOptions => {
        const dbInfo = TestDatabase.getDbInfo();
        return {
          type: 'postgres',
          ...dbInfo,
          entities: [
            ItemGroup,
            Item,
            PriceList,
            ItemPrice,
            PosProfile,
            Customer,
            ModeOfPayment,
            Company,
            Order,
            OrderItem,
            Payment,
          ],
          namingStrategy: new SnakeNamingStrategy(),
          synchronize: false,
          dropSchema: false,
          logging: false,
        };
      },
    }),
    ItemGroupModule,
    ItemModule,
    PricingModule,
    PosProfileModule,
    CustomerModule,
    ModeOfPaymentModule,
    CompanyModule,
    OrderModule,
  ],
})
export class TestAppModule {}
