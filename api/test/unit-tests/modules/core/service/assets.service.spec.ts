import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { AssetsService } from 'src/modules/core/service/assets.service';
import { of, throwError } from 'rxjs';
import * as path from 'path';
import { promises as fsp } from 'fs';

describe('AssetsService', () => {
  let service: AssetsService;
  let httpService: jest.Mocked<HttpService>;

  // Mock fs and fs/promises
  jest.mock('fs', () => ({
    createWriteStream: jest.fn().mockReturnValue({
      on: jest.fn().mockReturnThis(),
      pipe: jest.fn().mockReturnThis(),
    }),
  }));

  jest.mock('fs/promises', () => ({
    mkdir: jest.fn().mockResolvedValue(undefined),
    rm: jest.fn().mockResolvedValue(undefined),
    rmdir: jest.fn().mockResolvedValue(undefined),
    readdir: jest.fn().mockResolvedValue([]),
  }));

  beforeEach(async () => {
    // Reset all mocks first
    jest.clearAllMocks();

    // Mock environment variable
    process.env.ERP_NEXT_URL = 'https://erp.example.com';
    process.env.FRAPPE_SITE_NAME = 'my-frappe-site';

    // Mock Logger to suppress console output during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AssetsService,
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AssetsService>(AssetsService);
    httpService = module.get(HttpService);
  });

  afterEach(async () => {
    delete process.env.ERP_NEXT_URL;
    delete process.env.FRAPPE_SITE_NAME;

    // Clean up any test assets directory
    const testAssetsDir = path.join(process.cwd(), 'assets');
    try {
      await fsp.rm(testAssetsDir, { recursive: true, force: true });
    } catch {
      // Ignore if directory doesn't exist
    }
  });

  describe('handleAssetForDoctype', () => {
    const mockParams = {
      erpUrl: '/files/logo.png',
      doctype: 'Company',
      doctypeId: 'TestCompany',
      assetField: 'company_logo',
    };

    it('should return null for invalid asset URL', async () => {
      const result = await service.handleAssetForDoctype({
        ...mockParams,
        erpUrl: 'invalid-url',
      });

      expect(result).toBeNull();
    });

    it('should return null when ERP_NEXT_URL is not set', async () => {
      delete process.env.ERP_NEXT_URL;

      const result = await service.handleAssetForDoctype(mockParams);

      expect(result).toBeNull();
    });

    it('should successfully download and store asset', async () => {
      // Mock a successful HTTP response with a readable stream
      const mockStream = {
        pipe: jest.fn((writeStream: { emit: (event: string) => void }) => {
          // Simulate successful pipe operation
          setTimeout(() => writeStream.emit('finish'), 0);
          return writeStream;
        }),
      };

      httpService.get = jest.fn().mockReturnValue(
        of({
          status: 200,
          data: mockStream,
        }),
      );

      const result = await service.handleAssetForDoctype(mockParams);

      expect(httpService.get).toHaveBeenCalledWith(
        'https://erp.example.com/files/logo.png',
        {
          responseType: 'stream',
          headers: { Host: process.env.FRAPPE_SITE_NAME },
          validateStatus: expect.any(Function),
        },
      );
      expect(result).toBe('/assets/company/testcompany/company_logo/logo.png');
    });

    it('should return null when HTTP request fails', async () => {
      const networkError = new Error('Network error');
      httpService.get = jest
        .fn()
        .mockReturnValue(throwError(() => networkError));

      const result = await service.handleAssetForDoctype(mockParams);

      expect(result).toBeNull();
    });

    it('should return null when response is not a stream', async () => {
      httpService.get = jest.fn().mockReturnValue(
        of({
          data: 'not a stream',
        }),
      );

      const result = await service.handleAssetForDoctype(mockParams);

      expect(result).toBeNull();
    });

    it('should clean up previous asset if provided', async () => {
      // Mock the deleteAsset method
      const deleteAssetSpy = jest
        .spyOn(service, 'deleteAsset')
        .mockResolvedValue();

      // Mock HTTP failure to avoid dealing with stream complexity
      const networkError = new Error('Network error');
      httpService.get = jest
        .fn()
        .mockReturnValue(throwError(() => networkError));

      await service.handleAssetForDoctype({
        ...mockParams,
        previousLocalPath:
          '/assets/company/testcompany/company_logo/old-logo.png',
      });

      expect(deleteAssetSpy).toHaveBeenCalledWith(
        '/assets/company/testcompany/company_logo/old-logo.png',
      );
      deleteAssetSpy.mockRestore();
    });

    it('should not clean up if previous asset path is not a local asset', async () => {
      const deleteAssetSpy = jest
        .spyOn(service, 'deleteAsset')
        .mockResolvedValue();

      // Mock HTTP failure to avoid dealing with stream complexity
      const networkError = new Error('Network error');
      httpService.get = jest
        .fn()
        .mockReturnValue(throwError(() => networkError));

      await service.handleAssetForDoctype({
        ...mockParams,
        previousLocalPath: 'https://erp.example.com/files/old-logo.png',
      });

      expect(deleteAssetSpy).not.toHaveBeenCalled();
      deleteAssetSpy.mockRestore();
    });

    it('should not clean up if previous asset path is null/undefined', async () => {
      const deleteAssetSpy = jest
        .spyOn(service, 'deleteAsset')
        .mockResolvedValue();

      // Mock HTTP failure to avoid dealing with stream complexity
      const networkError = new Error('Network error');
      httpService.get = jest
        .fn()
        .mockReturnValue(throwError(() => networkError));

      await service.handleAssetForDoctype(mockParams);

      expect(deleteAssetSpy).not.toHaveBeenCalled();
      deleteAssetSpy.mockRestore();
    });
  });

  describe('deleteAsset', () => {
    it('should warn for invalid local asset path', async () => {
      await service.deleteAsset('invalid-path');

      expect(Logger.prototype.warn).toHaveBeenCalledWith(
        'Invalid local asset path: invalid-path',
      );
    });

    it('should warn for non-assets path', async () => {
      await service.deleteAsset('/other/path/file.png');

      expect(Logger.prototype.warn).toHaveBeenCalledWith(
        'Invalid local asset path: /other/path/file.png',
      );
    });

    it('should warn for null/undefined path', async () => {
      await service.deleteAsset('');

      expect(Logger.prototype.warn).toHaveBeenCalledWith(
        'Invalid local asset path: ',
      );
    });
  });

  describe('sanitizePath', () => {
    it('should sanitize special characters', () => {
      expect(service.sanitizePath('Company Name')).toBe('company-name');
      expect(service.sanitizePath('Test Company (Ltd)')).toBe(
        'test-company--ltd-',
      );
      expect(service.sanitizePath('Item@Group#123')).toBe('item-group-123');
      expect(service.sanitizePath('Normal_Name')).toBe('normal_name');
    });

    it('should convert to lowercase', () => {
      expect(service.sanitizePath('UPPERCASE')).toBe('uppercase');
      expect(service.sanitizePath('MixedCase')).toBe('mixedcase');
    });

    it('should preserve valid characters', () => {
      expect(service.sanitizePath('valid-name_123')).toBe('valid-name_123');
    });

    it('should handle empty strings', () => {
      expect(service.sanitizePath('')).toBe('');
    });

    it('should handle strings with only special characters', () => {
      expect(service.sanitizePath('!@#$%^&*()')).toBe('----------');
    });
  });

  describe('service instantiation', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have HttpService injected', () => {
      expect(httpService).toBeDefined();
    });

    it('should have logger defined', () => {
      // Just verify the service is properly instantiated
      expect(service).toBeInstanceOf(AssetsService);
    });
  });
});
