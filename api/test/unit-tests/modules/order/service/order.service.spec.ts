import { Test, TestingModule } from '@nestjs/testing';
import { OrderService } from '../../../../../src/modules/order/service/order.service';
import { Order } from '../../../../../src/modules/order/entity/order.entity';
import { OrderItem } from '../../../../../src/modules/order/entity/order-item.entity';
import { Payment } from '../../../../../src/modules/order/entity/payment.entity';
import { ModeOfPayment } from '../../../../../src/modules/mode-of-payment/entity/mode-of-payment.entity';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource, SelectQueryBuilder } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { OrderFactory } from '../../../../../src/modules/order/factory/order.factory';
import { OrderItemFactory } from '../../../../../src/modules/order/factory/order-item.factory';
import { PaymentFactory } from '../../../../../src/modules/order/factory/payment.factory';

describe('OrderService', () => {
  let service: OrderService;
  let mockQueryBuilder: Partial<SelectQueryBuilder<Order>>;

  const mockOrderRepository = {
    createQueryBuilder: jest.fn(),
  };

  const mockOrderItemRepository = {};
  const mockPaymentRepository = {};
  const mockModeOfPaymentRepository = {};
  const mockDataSource = {};
  const mockEventEmitter = {};
  const mockOrderFactory = {};
  const mockOrderItemFactory = {};
  const mockPaymentFactory = {};

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();

    // Setup query builder mock
    mockQueryBuilder = {
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
    };

    mockOrderRepository.createQueryBuilder.mockReturnValue(mockQueryBuilder);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrderService,
        {
          provide: getRepositoryToken(Order),
          useValue: mockOrderRepository,
        },
        {
          provide: getRepositoryToken(OrderItem),
          useValue: mockOrderItemRepository,
        },
        {
          provide: getRepositoryToken(Payment),
          useValue: mockPaymentRepository,
        },
        {
          provide: getRepositoryToken(ModeOfPayment),
          useValue: mockModeOfPaymentRepository,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: OrderFactory,
          useValue: mockOrderFactory,
        },
        {
          provide: OrderItemFactory,
          useValue: mockOrderItemFactory,
        },
        {
          provide: PaymentFactory,
          useValue: mockPaymentFactory,
        },
      ],
    }).compile();

    service = module.get<OrderService>(OrderService);
  });

  describe('generateDisplayId', () => {
    it('should return "1" for the first order of the day', async () => {
      // Mock no existing orders for today
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(null);

      const result = await service['generateDisplayId']();

      expect(result).toBe('1');
      expect(mockOrderRepository.createQueryBuilder).toHaveBeenCalledWith(
        'order',
      );
      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'DATE(order.created_at) = :today',
        { today: expect.stringMatching(/\d{4}-\d{2}-\d{2}/) },
      );
      expect(mockQueryBuilder.orderBy).toHaveBeenCalledWith(
        'order.display_id',
        'DESC',
      );
    });

    it('should increment the display_id when orders exist for today', async () => {
      // Mock existing order with display_id "105"
      const mockLastOrder = { displayId: '105' } as Order;
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(mockLastOrder);

      const result = await service['generateDisplayId']();

      expect(result).toBe('106');
    });

    it('should handle display_id increment from "199" to "200"', async () => {
      // Mock existing order with display_id "199"
      const mockLastOrder = { displayId: '199' } as Order;
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(mockLastOrder);

      const result = await service['generateDisplayId']();

      expect(result).toBe('200');
    });

    it('should use current date in YYYY-MM-DD format for query', async () => {
      (mockQueryBuilder.getOne as jest.Mock).mockResolvedValue(null);

      // Mock Date to return a specific date
      const mockDate = new Date('2024-03-15T10:30:00Z');
      jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
      jest
        .spyOn(mockDate, 'toISOString')
        .mockReturnValue('2024-03-15T10:30:00.000Z');

      await service['generateDisplayId']();

      expect(mockQueryBuilder.where).toHaveBeenCalledWith(
        'DATE(order.created_at) = :today',
        { today: '2024-03-15' },
      );

      jest.clearAllMocks();
      jest.restoreAllMocks();
    });
  });
});
