import { INestApplication, ValidationPipe } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { Order } from 'src/modules/order/entity/order.entity';
import { OrderItem } from 'src/modules/order/entity/order-item.entity';
import { Payment } from 'src/modules/order/entity/payment.entity';
import { ModeOfPayment } from 'src/modules/mode-of-payment/entity/mode-of-payment.entity';
import { CreateOrderRequestDto } from 'src/modules/order/dto/create-order-request.dto';
import { PaymentStatus } from 'src/modules/order/entity/payment.entity';
import * as request from 'supertest';
import { DataSource, Not, IsNull } from 'typeorm';
import { TestAppModule } from '../../../../test-app.module';
import { TestDatabaseModule } from '../../../../test-database.module';

/**
 * E2E test for OrderController using a real PostgreSQL database via Testcontainers.
 */
describe('OrderController (e2e)', () => {
  let app: INestApplication;
  let dataSource: DataSource;
  let apiKey: string;
  let httpServer: ReturnType<INestApplication['getHttpServer']>;

  const testModeOfPayments: ModeOfPayment[] = [
    {
      name: 'Cash',
      mode_of_payment: 'Cash',
      type: 'Cash',
      enabled: 1,
    } as ModeOfPayment,
    {
      name: 'Credit Card',
      mode_of_payment: 'Credit Card',
      type: 'Bank',
      enabled: 1,
    } as ModeOfPayment,
  ];

  beforeAll(async () => {
    // Set up API key for authentication
    apiKey = 'test-api-key-e2e';
    process.env.API_KEY = apiKey;

    const moduleFixture = await Test.createTestingModule({
      imports: [TestAppModule, TestDatabaseModule],
    }).compile();
    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    app.setGlobalPrefix('api/v1');
    await app.init();
    httpServer = app.getHttpServer();
    dataSource = moduleFixture.get(DataSource);

    // Clear tables using delete with criteria
    await dataSource
      .getRepository(Payment)
      .delete({ paymentId: Not(IsNull()) });
    await dataSource.getRepository(OrderItem).delete({ lineId: Not(IsNull()) });
    await dataSource.getRepository(Order).delete({ localId: Not(IsNull()) });

    for (const modeOfPayment of testModeOfPayments) {
      await dataSource.getRepository(ModeOfPayment).save(modeOfPayment);
    }
  });

  afterAll(async () => {
    // Clean up test data using delete with criteria
    await dataSource
      .getRepository(Payment)
      .delete({ paymentId: Not(IsNull()) });
    await dataSource.getRepository(OrderItem).delete({ lineId: Not(IsNull()) });
    await dataSource.getRepository(Order).delete({ localId: Not(IsNull()) });

    // Clean up environment variable
    delete process.env.API_KEY;
    await app.close();
    await dataSource.destroy();
  });

  beforeEach(async () => {
    // Clear order-related tables before each test using delete with criteria
    await dataSource
      .getRepository(Payment)
      .delete({ paymentId: Not(IsNull()) });
    await dataSource.getRepository(OrderItem).delete({ lineId: Not(IsNull()) });
    await dataSource.getRepository(Order).delete({ localId: Not(IsNull()) });
  });

  describe('POST /orders', () => {
    it('should create a complete order with items and payment', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 25.5,
        currency: 'USD',
        posProfile: 'Test POS Profile',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item 1',
            quantity: 2,
            unitPrice: 10.0,
            currency: 'USD',
            lineTotal: 20.0,
            customizations: { size: 'large', notes: 'extra sauce' },
          },
          {
            itemCode: 'ITEM002',
            itemName: 'Test Item 2',
            quantity: 1,
            unitPrice: 5.5,
            currency: 'USD',
            lineTotal: 5.5,
          },
        ],
        payments: [
          {
            amount: 25.5,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN123456',
          },
        ],
      };

      const response: request.Response = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(201);

      // Verify response structure
      expect(response.body).toMatchObject({
        displayId: expect.any(String),
        customer: 'Test Customer',
        totalAmount: '25.50',
        currency: 'USD',
        posProfile: 'Test POS Profile',
        syncStatus: 'Pending',
        orderStatus: 'Paid',
        localId: expect.any(String),
        createdAt: expect.any(String),
      });

      expect(response.body.items).toHaveLength(2);
      expect(response.body.items[0]).toMatchObject({
        itemCode: 'ITEM001',
        itemName: 'Test Item 1',
        quantity: 2,
        unitPrice: '10.00',
        currency: 'USD',
        lineTotal: '20.00',
      });
      expect(response.body.items[1]).toMatchObject({
        itemCode: 'ITEM002',
        itemName: 'Test Item 2',
        quantity: 1,
        unitPrice: '5.50',
        currency: 'USD',
        lineTotal: '5.50',
      });

      expect(response.body.payments).toHaveLength(1);
      expect(response.body.payments[0]).toMatchObject({
        amount: '25.50',
        currency: 'USD',
        status: 'Completed',
        transactionReference: 'TXN123456',
        method: expect.objectContaining({
          name: 'Cash',
        }),
      });

      // Verify database records were created
      const orderRepo = dataSource.getRepository(Order);
      const orderItemRepo = dataSource.getRepository(OrderItem);
      const paymentRepo = dataSource.getRepository(Payment);

      const createdOrder = await orderRepo.findOne({
        where: { localId: response.body.localId },
        relations: ['items', 'payments'],
      });

      expect(createdOrder).toBeDefined();
      expect(createdOrder).not.toBeNull();
      expect(createdOrder!.items).toHaveLength(2);
      expect(createdOrder!.payments).toBeDefined();
      expect(createdOrder!.payments).toHaveLength(1);

      const orderItems = await orderItemRepo.find({
        where: { order: { localId: response.body.localId } },
      });
      expect(orderItems).toHaveLength(2);

      const payment = await paymentRepo.findOne({
        where: { order: { localId: response.body.localId } },
      });
      expect(payment).toBeDefined();
    });

    it('should generate sequential display_id starting from 1', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 10.0,
        currency: 'USD',
        posProfile: 'Test POS',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 10.0,
            currency: 'USD',
            lineTotal: 10.0,
          },
        ],
        payments: [
          {
            amount: 10.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN1',
          },
        ],
      };

      // Create first order
      const response1 = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(201);

      expect(response1.body.displayId).toBe('1');

      // Create second order
      const response2 = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send({
          ...createOrderDto,
          payments: [
            { ...createOrderDto.payments[0], transactionReference: 'TXN2' },
          ],
        })
        .expect(201);

      expect(response2.body.displayId).toBe('2');
    });

    it('should use default customer when not provided', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 5.0,
        currency: 'USD',
        posProfile: 'Test POS',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 5.0,
            currency: 'USD',
            lineTotal: 5.0,
          },
        ],
        payments: [
          {
            amount: 5.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN123',
          },
        ],
      };

      const response = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(201);

      expect(response.body.customer).toBe('Test Customer');
    });

    it('should return 404 when mode of payment not found', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 10.0,
        currency: 'USD',
        posProfile: 'Test POS',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 10.0,
            currency: 'USD',
            lineTotal: 10.0,
          },
        ],
        payments: [
          {
            amount: 10.0,
            currency: 'USD',
            method: 'NonExistentPaymentMethod',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN123',
          },
        ],
      };

      await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(404);
    });

    it('should validate required fields', async () => {
      const invalidOrderDto = {
        totalAmount: 'invalid',
        // missing required fields
      };

      await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(invalidOrderDto)
        .expect(400);
    });

    it('should require authentication', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 10.0,
        currency: 'USD',
        posProfile: 'Test POS',
        items: [],
        payments: [
          {
            amount: 10.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN123',
          },
        ],
      };

      await request(httpServer)
        .post('/api/v1/orders')
        .send(createOrderDto)
        .expect(401);
    });
  });

  describe('GET /orders', () => {
    it('should return all orders', async () => {
      // Create a test order first
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 15.0,
        currency: 'USD',
        posProfile: 'Test POS',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 15.0,
            currency: 'USD',
            lineTotal: 15.0,
          },
        ],
        payments: [
          {
            amount: 15.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN123',
          },
        ],
      };

      await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto);

      const response = await request(httpServer)
        .get('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    it('should require authentication', async () => {
      await request(httpServer).get('/api/v1/orders').expect(401);
    });
  });

  describe('POST /orders - Multiple Payments', () => {
    it('should create an order with multiple payments successfully', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Multi-Payment Customer',
        totalAmount: 50.0,
        currency: 'USD',
        posProfile: 'Test POS Profile',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item 1',
            quantity: 1,
            unitPrice: 50.0,
            currency: 'USD',
            lineTotal: 50.0,
          },
        ],
        payments: [
          {
            amount: 30.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN001',
          },
          {
            amount: 20.0,
            currency: 'USD',
            method: 'Credit Card',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN002',
          },
        ],
      };

      const response: request.Response = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(201);

      // Verify response structure
      expect(response.body).toMatchObject({
        displayId: expect.any(String),
        customer: 'Multi-Payment Customer',
        totalAmount: '50.00',
        currency: 'USD',
        posProfile: 'Test POS Profile',
        syncStatus: 'Pending',
        orderStatus: 'Paid',
        localId: expect.any(String),
        createdAt: expect.any(String),
      });

      // Verify payments array
      expect(response.body.payments).toHaveLength(2);
      expect(response.body.payments[0]).toMatchObject({
        amount: '30.00',
        currency: 'USD',
        status: 'Completed',
        transactionReference: 'TXN001',
        method: expect.objectContaining({
          name: 'Cash',
        }),
      });
      expect(response.body.payments[1]).toMatchObject({
        amount: '20.00',
        currency: 'USD',
        status: 'Completed',
        transactionReference: 'TXN002',
        method: expect.objectContaining({
          name: 'Credit Card',
        }),
      });

      // Verify database records
      const orderRepository = dataSource.getRepository(Order);

      const savedOrder = await orderRepository.findOne({
        where: { localId: response.body.localId },
        relations: ['payments', 'payments.method'],
      });

      expect(savedOrder).toBeDefined();
      expect(savedOrder).not.toBeNull();
      expect(savedOrder!.payments).toHaveLength(2);

      const paymentAmounts = savedOrder!.payments.map((p) =>
        parseFloat(p.amount.toString()),
      );
      expect(paymentAmounts).toContain(30.0);
      expect(paymentAmounts).toContain(20.0);
    });

    it('should reject order when payment methods are not found', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Test Customer',
        totalAmount: 25.0,
        currency: 'USD',
        posProfile: 'Test POS Profile',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 25.0,
            currency: 'USD',
            lineTotal: 25.0,
          },
        ],
        payments: [
          {
            amount: 15.0,
            currency: 'USD',
            method: 'NonExistentMethod',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN001',
          },
          {
            amount: 10.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN002',
          },
        ],
      };

      const response = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(404);

      expect(response.body.message).toContain(
        'Payment methods not found: NonExistentMethod',
      );
    });

    it('should validate payment array is not empty', async () => {
      const createOrderDto = {
        customer: 'Test Customer',
        totalAmount: 25.0,
        currency: 'USD',
        posProfile: 'Test POS Profile',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 25.0,
            currency: 'USD',
            lineTotal: 25.0,
          },
        ],
        payments: [],
      };

      await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(400);
    });

    it('should create order with single payment using new payments array structure', async () => {
      const createOrderDto: CreateOrderRequestDto = {
        customer: 'Single Payment Customer',
        totalAmount: 15.0,
        currency: 'USD',
        posProfile: 'Test POS Profile',
        items: [
          {
            itemCode: 'ITEM001',
            itemName: 'Test Item',
            quantity: 1,
            unitPrice: 15.0,
            currency: 'USD',
            lineTotal: 15.0,
          },
        ],
        payments: [
          {
            amount: 15.0,
            currency: 'USD',
            method: 'Cash',
            status: PaymentStatus.COMPLETED,
            transactionReference: 'TXN001',
          },
        ],
      };

      const response: request.Response = await request(httpServer)
        .post('/api/v1/orders')
        .set('Authorization', `Bearer ${apiKey}`)
        .send(createOrderDto)
        .expect(201);

      expect(response.body.payments).toHaveLength(1);
      expect(response.body.payments[0]).toMatchObject({
        amount: '15.00',
        currency: 'USD',
        status: 'Completed',
        transactionReference: 'TXN001',
      });
    });
  });
});
