{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test:unit": "jest --config ./test/unit-tests/jest.unit.config.js --verbose", "test:unit:watch": "jest --watch --config ./test/unit-tests/jest.unit.config.js", "test:unit:cov": "jest --coverage --config ./test/unit-tests/jest.unit.config.js", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand --config ./test/unit-tests/jest.unit.config.js", "test:e2e": "jest --config ./test/e2e-tests/jest.e2e.config.js --verbose", "test:e2e:cov": "jest --config ./test/e2e-tests/jest.e2e.config.js --coverage --coverageDirectory=./coverage/e2e"}, "dependencies": {"@nats-io/jetstream": "^3.0.2", "@nestjs/axios": "^4.0.1", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/microservices": "^11.1.0", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.13", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.1.1", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.13", "axios": "^1.11.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "nats": "^2.29.3", "pg": "^8.15.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.22", "typeorm-naming-strategies": "^4.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@testcontainers/postgresql": "^11.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "testcontainers": "^11.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "roots": ["<rootDir>/src", "<rootDir>/test"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "coverage", "testEnvironment": "node"}}