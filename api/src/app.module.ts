import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ServeStaticModule } from '@nestjs/serve-static';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import { join } from 'path';
import { ItemGroupModule } from './modules/item-group/item-group.module';
import { JetstreamModule } from './modules/jetstream/jetstream.module';
import { HealthModule } from './modules/health/health.module';
import { ItemModule } from './modules/item/item.module';
import { PricingModule } from './modules/pricing/pricing.module';
import { PosProfileModule } from './modules/pos-profile/pos-profile.module';
import { AuthModule } from './modules/auth/auth.module';
import { CustomerModule } from './modules/customer/customer.module';
import { ModeOfPaymentModule } from './modules/mode-of-payment/mode-of-payment.module';
import { CompanyModule } from './modules/company/company.module';
import { OrderModule } from './modules/order/order.module';

const typeOrmModuleOptions: TypeOrmModuleOptions = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: Number(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  namingStrategy: new SnakeNamingStrategy(),
  synchronize: process.env.NODE_ENV === 'development',
  autoLoadEntities: true,
  ssl: Boolean(JSON.parse(process.env.DATABASE_SSL || 'false')),
  // migrationsRun: true,
  // migrations: [__dirname + '/migrations/*{.ts,.js}'],
};

@Module({
  imports: [
    TypeOrmModule.forRoot(typeOrmModuleOptions),
    EventEmitterModule.forRoot(),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'assets'),
      serveRoot: '/assets',
    }),
    AuthModule,
    HealthModule,
    ItemGroupModule,
    ItemModule,
    JetstreamModule,
    PricingModule,
    PosProfileModule,
    CustomerModule,
    ModeOfPaymentModule,
    CompanyModule,
    OrderModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
