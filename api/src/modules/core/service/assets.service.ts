import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import * as path from 'path';
import * as fs from 'fs';
import { promises as fsp } from 'fs';

interface AssetDownloadParams {
  erpUrl: string;
  doctype: string;
  doctypeId: string;
  assetField: string;
  previousLocalPath?: string;
}

interface AssetPathInfo {
  safeDoctype: string;
  safeDoctypeId: string;
  safeAssetField: string;
  safeFilename: string;
  assetsDir: string;
  localFilePath: string;
  localAssetPath: string;
}

@Injectable()
export class AssetsService {
  private readonly logger = new Logger(AssetsService.name);
  private readonly ASSETS_ROOT = 'assets';
  private readonly ASSETS_PATH_PREFIX = '/assets/';

  constructor(private readonly httpService: HttpService) {}

  /**
   * Download and store an asset for a given doctype and field.
   * Cleans up previous asset if previousLocalPath is provided.
   * Returns the new relative path or null if download fails.
   */
  async handleAssetForDoctype(
    params: AssetDownloadParams,
  ): Promise<string | null> {
    const { erpUrl, doctype, doctypeId, assetField, previousLocalPath } =
      params;

    if (!this.isValidAssetUrl(erpUrl)) {
      this.logger.warn(`Invalid asset URL: ${erpUrl}`);
      return null;
    }

    // Clean up previous asset if provided
    if (previousLocalPath && this.isValidLocalAssetPath(previousLocalPath)) {
      await this.deleteAsset(previousLocalPath);
    }

    try {
      const assetPathInfo = this.buildAssetPathInfo(
        doctype,
        doctypeId,
        assetField,
        erpUrl,
      );
      await this.ensureAssetsDirectory(assetPathInfo.assetsDir);

      const success = await this.downloadAndStoreAsset(
        erpUrl,
        assetPathInfo.localFilePath,
      );

      if (success) {
        this.logger.log(
          `Asset stored successfully: ${assetPathInfo.localAssetPath}`,
        );
        return assetPathInfo.localAssetPath;
      }

      return null;
    } catch (error) {
      this.logger.error(
        `Error handling asset for ${doctype} ${doctypeId}:`,
        error instanceof Error ? error.message : 'Unknown error',
      );
      return null;
    }
  }

  /**
   * Deletes a specific asset by its local path
   */
  async deleteAsset(localAssetPath: string): Promise<void> {
    try {
      if (!this.isValidLocalAssetPath(localAssetPath)) {
        this.logger.warn(`Invalid local asset path: ${localAssetPath}`);
        return;
      }

      const fullPath = this.getFullAssetPath(localAssetPath);
      await this.removeFileIfExists(fullPath);
      await this.cleanupEmptyDirectories(path.dirname(fullPath));
    } catch (error) {
      this.logger.error(`Error deleting asset ${localAssetPath}:`, error);
    }
  }

  /**
   * Sanitizes a path component for safe file system usage
   */
  public sanitizePath(pathComponent: string): string {
    return pathComponent.replace(/[^a-zA-Z0-9-_]/g, '-').toLowerCase();
  }

  /**
   * Validates if the provided URL is a valid asset URL
   */
  private isValidAssetUrl(url: string): boolean {
    return !!url && url.startsWith('/files');
  }

  /**
   * Validates if the provided path is a valid local asset path
   */
  private isValidLocalAssetPath(localPath: string): boolean {
    return !!localPath && localPath.startsWith(this.ASSETS_PATH_PREFIX);
  }

  /**
   * Builds the complete asset path information
   */
  private buildAssetPathInfo(
    doctype: string,
    doctypeId: string,
    assetField: string,
    erpUrl: string,
  ): AssetPathInfo {
    const safeDoctype = this.sanitizePath(doctype);
    const safeDoctypeId = this.sanitizePath(doctypeId);
    const safeAssetField = this.sanitizePath(assetField);

    const assetsDir = path.join(
      process.cwd(),
      this.ASSETS_ROOT,
      safeDoctype,
      safeDoctypeId,
      safeAssetField,
    );

    const originalFilename = decodeURIComponent(path.basename(erpUrl));
    const safeFilename = originalFilename.replace(/[^a-zA-Z0-9.-]/g, '-');
    const localFilePath = path.join(assetsDir, safeFilename);
    const localAssetPath = `${this.ASSETS_PATH_PREFIX}${safeDoctype}/${safeDoctypeId}/${safeAssetField}/${safeFilename}`;

    return {
      safeDoctype,
      safeDoctypeId,
      safeAssetField,
      safeFilename,
      assetsDir,
      localFilePath,
      localAssetPath,
    };
  }

  /**
   * Ensures the assets directory exists
   */
  private async ensureAssetsDirectory(assetsDir: string): Promise<void> {
    await fsp.mkdir(assetsDir, { recursive: true });
  }

  /**
   * Downloads and stores an asset from ERPNext
   */
  private async downloadAndStoreAsset(
    erpUrl: string,
    localFilePath: string,
  ): Promise<boolean> {
    const erpNextUrl = process.env.ERP_NEXT_URL;
    if (!erpNextUrl) {
      this.logger.error('ERP_NEXT_URL environment variable not set');
      return false;
    }

    const fullUrl = erpNextUrl + erpUrl;
    const headers = {
      Host: process.env.FRAPPE_SITE_NAME,
    };

    try {
      const response$ = this.httpService.get(fullUrl, {
        responseType: 'stream' as const,
        headers,
        validateStatus: (status) => status < 500,
      });

      const response = await lastValueFrom(response$);

      if (response.status !== 200) {
        this.logger.error(
          `Asset download failed with status ${response.status}: ${fullUrl}`,
        );
        return false;
      }

      const stream = response.data as NodeJS.ReadableStream;
      if (!this.isValidStream(stream)) {
        this.logger.error('Asset download did not return a valid stream');
        return false;
      }

      await this.writeStreamToFile(stream, localFilePath);
      return true;
    } catch (error) {
      this.logger.error(`Error downloading asset from ${fullUrl}:`, error);
      return false;
    }
  }

  /**
   * Validates if the provided object is a valid readable stream
   */
  private isValidStream(stream: unknown): stream is NodeJS.ReadableStream {
    return (
      !!stream && typeof (stream as NodeJS.ReadableStream).pipe === 'function'
    );
  }

  /**
   * Writes a stream to a file
   */
  private async writeStreamToFile(
    stream: NodeJS.ReadableStream,
    filePath: string,
  ): Promise<void> {
    const writer = fs.createWriteStream(filePath);
    stream.pipe(writer);

    return new Promise<void>((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', (err) => {
        reject(err instanceof Error ? err : new Error('Stream write error'));
      });
    });
  }

  /**
   * Gets the full file system path for a local asset path
   */
  private getFullAssetPath(localAssetPath: string): string {
    const relativePath = localAssetPath.replace(this.ASSETS_PATH_PREFIX, '');
    return path.join(process.cwd(), this.ASSETS_ROOT, relativePath);
  }

  /**
   * Removes a file if it exists, ignoring ENOENT errors
   */
  private async removeFileIfExists(filePath: string): Promise<void> {
    try {
      await fsp.rm(filePath);
      this.logger.log(`Deleted asset: ${filePath}`);
    } catch (err) {
      if ((err as NodeJS.ErrnoException).code !== 'ENOENT') {
        throw err;
      }
    }
  }

  /**
   * Cleans up empty directories recursively
   */
  private async cleanupEmptyDirectories(dirPath: string): Promise<void> {
    try {
      const assetsRoot = path.join(process.cwd(), this.ASSETS_ROOT);

      while (dirPath.startsWith(assetsRoot) && dirPath !== assetsRoot) {
        const files = await fsp.readdir(dirPath);

        if (files.length === 0) {
          await fsp.rmdir(dirPath);
          this.logger.log(`Removed empty directory: ${dirPath}`);
          dirPath = path.dirname(dirPath);
        } else {
          break;
        }
      }
    } catch (error) {
      this.logger.error('Error cleaning up empty directories:', error);
    }
  }
}
