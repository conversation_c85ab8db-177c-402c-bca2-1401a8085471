import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { Company } from '../entity/company.entity';
import { AssetsService } from '../../core/service/assets.service';

@Injectable()
export class CompanyService {
  constructor(
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    private readonly assetsService: AssetsService,
  ) {}

  async createOrUpdateCompany(company: Company): Promise<InsertResult> {
    let previousLogoPath: string | undefined = undefined;
    if (company.company_logo) {
      // If updating, get the previous logo path
      const existing = company.name
        ? await this.companyRepository.findOne({
            where: { name: company.name },
          })
        : undefined;
      if (
        existing &&
        existing.company_logo &&
        existing.company_logo.startsWith('/assets/')
      ) {
        previousLogoPath = existing.company_logo;
      }
      const newLogoPath = await this.assetsService.handleAssetForDoctype({
        erpUrl: company.company_logo,
        doctype: 'Company',
        doctypeId: company.name,
        assetField: 'company_logo',
        previousLocalPath: previousLogoPath,
      });
      if (newLogoPath) {
        company.company_logo = newLogoPath;
      }
    }
    return this.companyRepository.upsert(company, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteCompany(company: Company): Promise<void> {
    if (company.company_logo && company.company_logo.startsWith('/assets/')) {
      await this.assetsService.deleteAsset(company.company_logo);
    }
    await this.companyRepository.delete(company.name);
  }

  async findCompanies(): Promise<Company[]> {
    return this.companyRepository.find();
  }

  async findCompanyByName(name: string): Promise<Company | null> {
    return this.companyRepository.findOne({ where: { name } });
  }
}
