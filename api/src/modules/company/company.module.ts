import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Company } from './entity/company.entity';
import { CompanyService } from './service/company.service';
import { CompanyController } from './controller/company.controller';
import { CompanyUpdateHandler } from './jetstream-handler/company-update.handler';
import { CompanyDeleteHandler } from './jetstream-handler/company-delete.handler';
import { CoreModule } from '../core/core.module';

@Module({
  imports: [TypeOrmModule.forFeature([Company]), CoreModule],
  controllers: [CompanyController],
  providers: [CompanyService, CompanyUpdateHandler, CompanyDeleteHandler],
  exports: [CompanyService, CompanyUpdateHandler, CompanyDeleteHandler],
})
export class CompanyModule {}
