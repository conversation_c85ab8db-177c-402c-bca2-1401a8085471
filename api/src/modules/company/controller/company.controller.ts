import { Controller, Get, UseGuards, Delete, Param } from '@nestjs/common';
import { CompanyService } from '../service/company.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('companies')
@UseGuards(ApiKeyGuard)
export class CompanyController {
  constructor(private readonly companyService: CompanyService) {}

  @Get()
  async getCompanies() {
    return this.companyService.findCompanies();
  }

  @Delete(':name')
  async deleteCompany(@Param('name') name: string) {
    const company = await this.companyService.findCompanyByName(name);
    if (!company) {
      return { success: false, message: `Company '${name}' not found` };
    }

    await this.companyService.deleteCompany(company);
    return { success: true, message: `Company '${name}' deleted successfully` };
  }
}
