import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { Item } from '../entity/item.entity';
import { AssetsService } from 'src/modules/core/service/assets.service';

@Injectable()
export class ItemService {
  constructor(
    @InjectRepository(Item)
    private readonly itemRepository: Repository<Item>,
    private readonly assetsService: AssetsService,
  ) {}

  async createOrUpdateItem(item: Item): Promise<InsertResult> {
    let previousLogoPath: string | undefined = undefined;
    if (item.image) {
      // If updating, get the previous logo path
      const existing = item.name
        ? await this.itemRepository.findOne({
            where: { name: item.name },
          })
        : undefined;
      if (existing && existing.image && existing.image.startsWith('/assets/')) {
        previousLogoPath = existing.image;
      }
      const newLogoPath = await this.assetsService.handleAssetForDoctype({
        erpUrl: item.image,
        doctype: 'Item',
        doctypeId: item.name,
        assetField: 'image',
        previousLocalPath: previousLogoPath,
      });
      if (newLogoPath) {
        item.image = newLogoPath;
      }
    }
    return this.itemRepository.upsert(item, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteItem(item: Item): Promise<void> {
    await this.itemRepository.delete(item.name);
  }

  async findItems(itemGroup?: string): Promise<Item[]> {
    if (itemGroup) {
      return this.itemRepository.find({ where: { item_group: itemGroup } });
    }
    return this.itemRepository.find();
  }
}
