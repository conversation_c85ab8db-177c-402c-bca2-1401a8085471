import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Item } from './entity/item.entity';
import { ItemService } from './service/item.service';
import { ItemUpdateHandler } from './jetstream-handler/item-update.handler';
import { ItemDeleteHandler } from './jetstream-handler/item-delete.handler';
import { ItemController } from './controller/item.controller';
import { CoreModule } from '../core/core.module';

@Module({
  imports: [TypeOrmModule.forFeature([Item]), CoreModule],
  controllers: [ItemController],
  providers: [ItemService, ItemUpdateHandler, ItemDeleteHandler],
  exports: [ItemService, ItemUpdateHandler, ItemDeleteHandler],
})
export class ItemModule {}
