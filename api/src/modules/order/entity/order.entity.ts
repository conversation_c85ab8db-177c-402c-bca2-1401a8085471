import {
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { OrderItem } from './order-item.entity';
import { Payment } from './payment.entity';

export enum OrderStatus {
  PAID = 'Paid',
  IN_PREPARATION = 'In Preparation',
  READY = 'Ready',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

export enum SyncStatus {
  PENDING = 'Pending',
  SYNCED = 'Synced',
  FAILED = 'Failed',
}

@Entity()
export class Order {
  @PrimaryGeneratedColumn('uuid')
  localId: string;

  @Column()
  displayId: string;

  @Column({ default: 'Walk-in Customer' })
  customer: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => OrderItem, (orderItem) => orderItem.order, { cascade: true })
  items: OrderItem[];

  @Column('decimal', { precision: 10, scale: 2 })
  totalAmount: number;

  @Column()
  currency: string;

  @Column()
  posProfile: string;

  @Column({
    type: 'enum',
    enum: SyncStatus,
    default: SyncStatus.PENDING,
  })
  syncStatus: SyncStatus;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PAID,
  })
  orderStatus: OrderStatus;

  @OneToMany(() => Payment, (payment) => payment.order, { cascade: true })
  payments: Payment[];
}
