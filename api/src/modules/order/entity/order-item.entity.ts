import {
  Column,
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Order } from './order.entity';

@Entity()
export class OrderItem {
  @PrimaryGeneratedColumn('uuid')
  lineId: string;

  @ManyToOne(() => Order, (order) => order.items)
  @JoinColumn({ name: 'order_id' })
  order: Order;

  @Column()
  itemCode: string;

  @Column()
  itemName: string;

  @Column('int')
  quantity: number;

  @Column('decimal', { precision: 10, scale: 2 })
  unitPrice: number;

  @Column()
  currency: string;

  @Column('jsonb', { nullable: true })
  customizations: any;

  @Column('decimal', { precision: 10, scale: 2 })
  lineTotal: number;
}
