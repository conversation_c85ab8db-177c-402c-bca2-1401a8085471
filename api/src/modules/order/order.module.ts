import { Module } from '@nestjs/common';
import { OrderGateway } from './gateway/order.gateway';
import { OrderService } from './service/order.service';
import { OrderController } from './controller/order.controller';
import { OrderCreatedLocalWebsocketSubscriber } from './subscriber/order.created.localWebsocket.subscriber';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Order } from './entity/order.entity';
import { OrderItem } from './entity/order-item.entity';
import { Payment } from './entity/payment.entity';
import { OrderFactory } from './factory/order.factory';
import { OrderItemFactory } from './factory/order-item.factory';
import { PaymentFactory } from './factory/payment.factory';
import { ModeOfPayment } from '../mode-of-payment/entity/mode-of-payment.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([Order, OrderItem, Payment, ModeOfPayment]),
  ],
  controllers: [OrderController],
  providers: [
    OrderGateway,
    OrderService,
    OrderCreatedLocalWebsocketSubscriber,
    OrderFactory,
    OrderItemFactory,
    PaymentFactory,
  ],
})
export class OrderModule {}
