import { Injectable, NotFoundException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, Repository } from 'typeorm';
import { Payment } from '../entity/payment.entity';
import { ModeOfPayment } from '../../mode-of-payment/entity/mode-of-payment.entity';
import { CreateOrderRequestDto } from '../dto/create-order-request.dto';
import { Order, OrderStatus } from '../entity/order.entity';
import { OrderEvents } from '../enum/orderEvents.enum';
import { OrderItemFactory } from '../factory/order-item.factory';
import { OrderFactory } from '../factory/order.factory';
import { PaymentFactory } from '../factory/payment.factory';
@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(ModeOfPayment)
    private readonly modeOfPaymentRepository: Repository<ModeOfPayment>,
    private readonly dataSource: DataSource,
    private readonly eventEmitter: EventEmitter2,
    private readonly orderFactory: OrderFactory,
    private readonly orderItemFactory: OrderItemFactory,
    private readonly paymentFactory: PaymentFactory,
  ) {}

  async findAll(): Promise<Order[]> {
    return this.orderRepository.find({
      relations: ['items', 'payments', 'payments.method'],
    });
  }

  async findById(localId: string): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: { localId },
      relations: ['items', 'payments', 'payments.method'],
    });
    if (!order) {
      throw new NotFoundException('Order not found');
    }
    return order;
  }

  async create(createOrderRequestDto: CreateOrderRequestDto): Promise<Order> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Generate display_id
      const displayId = await this.generateDisplayId();

      // Validate all payment methods exist
      const paymentMethods = new Set<string>();
      for (const paymentDto of createOrderRequestDto.payments) {
        paymentMethods.add(paymentDto.method);
      }

      const modesOfPayment = await this.modeOfPaymentRepository.find({
        where: [...paymentMethods].map((method) => ({ name: method })),
      });

      if (modesOfPayment.length !== paymentMethods.size) {
        const foundMethods = modesOfPayment.map((mode) => mode.name);
        const missingMethods = [...paymentMethods].filter(
          (method) => !foundMethods.includes(method),
        );
        throw new NotFoundException(
          `Payment methods not found: ${missingMethods.join(', ')}`,
        );
      }

      // Create order using factory
      const order = this.orderFactory.createOrder(
        createOrderRequestDto,
        displayId,
      );
      const savedOrder = await queryRunner.manager.save(order);

      // Create order items using factory
      const orderItems = this.orderItemFactory.createOrderItems(
        savedOrder,
        createOrderRequestDto.items,
      );
      await queryRunner.manager.save(orderItems);

      // Create payments using factory
      const payments: Payment[] = [];
      for (const paymentDto of createOrderRequestDto.payments) {
        const modeOfPayment = modesOfPayment.find(
          (mode) => mode.name === paymentDto.method,
        );
        if (!modeOfPayment) {
          throw new NotFoundException(
            `Payment method '${paymentDto.method}' not found`,
          );
        }
        const payment = this.paymentFactory.createPayment(
          savedOrder,
          modeOfPayment,
          paymentDto,
        );
        payments.push(payment);
      }
      await queryRunner.manager.save(payments);

      await queryRunner.commitTransaction();

      // Load the complete order with relations
      const completeOrder = await this.orderRepository.findOne({
        where: { localId: savedOrder.localId },
        relations: ['items', 'payments', 'payments.method'],
      });

      if (!completeOrder) {
        throw new NotFoundException('Order not found after creation');
      }

      this.eventEmitter.emit(OrderEvents.ORDER_CREATED, completeOrder);
      return completeOrder;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async generateDisplayId(): Promise<string> {
    try {
      // Get current date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];

      // Find the highest display_id for today
      const lastOrder = await this.orderRepository
        .createQueryBuilder('order')
        .where('DATE(order.created_at) = :today', { today })
        .orderBy('order.display_id', 'DESC')
        .getOne();

      if (!lastOrder) {
        // First order of the day, start at 1
        return '1';
      }

      // Increment the last display_id
      const lastDisplayId = parseInt(lastOrder.displayId);
      return (lastDisplayId + 1).toString();
    } catch (error) {
      // Log the error for debugging
      console.error('Error generating display ID:', error);
      throw new Error('Failed to generate display ID for order');
    }
  }

  async updateStatus(
    localId: string,
    orderStatus: OrderStatus,
  ): Promise<Order> {
    const order = await this.orderRepository.findOne({
      where: { localId },
      relations: ['items', 'payments', 'payments.method'],
    });

    if (!order) {
      throw new NotFoundException('Order not found');
    }

    order.orderStatus = orderStatus;
    const updatedOrder = await this.orderRepository.save(order);

    this.eventEmitter.emit(OrderEvents.ORDER_UPDATED, updatedOrder);
    return updatedOrder;
  }

  async delete(localId: string): Promise<void> {
    const order = await this.orderRepository.findOneBy({ localId });
    if (!order) {
      throw new NotFoundException('Order not found');
    }
    await this.orderRepository.delete(localId);
    this.eventEmitter.emit(OrderEvents.ORDER_DELETED, order);
  }
}
