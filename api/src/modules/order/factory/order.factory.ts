import { Injectable } from '@nestjs/common';
import { CreateOrderRequestDto } from '../dto/create-order-request.dto';
import { Order } from '../entity/order.entity';

@Injectable()
export class OrderFactory {
  createOrder(
    createOrderRequestDto: CreateOrderRequestDto,
    displayId: string,
  ): Order {
    const order = new Order();
    order.displayId = displayId;
    order.customer = createOrderRequestDto.customer;
    order.totalAmount = createOrderRequestDto.totalAmount;
    order.currency = createOrderRequestDto.currency;
    order.posProfile = createOrderRequestDto.posProfile;

    return order;
  }
}
