import { Injectable } from '@nestjs/common';
import { Payment } from '../entity/payment.entity';
import { Order } from '../entity/order.entity';
import { ModeOfPayment } from '../../mode-of-payment/entity/mode-of-payment.entity';
import { CreatePaymentDto } from '../dto/create-order-request.dto';

@Injectable()
export class PaymentFactory {
  createPayment(
    order: Order,
    modeOfPayment: ModeOfPayment,
    createPaymentDto: CreatePaymentDto,
  ): Payment {
    const payment = new Payment();
    payment.order = order;
    payment.amount = createPaymentDto.amount;
    payment.currency = createPaymentDto.currency;
    payment.method = modeOfPayment;
    payment.status = createPaymentDto.status;
    payment.transactionReference = createPaymentDto.transactionReference;

    return payment;
  }
}
