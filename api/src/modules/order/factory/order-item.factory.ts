import { Injectable } from '@nestjs/common';
import { OrderItem } from '../entity/order-item.entity';
import { Order } from '../entity/order.entity';
import { CreateOrderItemDto } from '../dto/create-order-request.dto';

@Injectable()
export class OrderItemFactory {
  createOrderItem(
    order: Order,
    createOrderItemDto: CreateOrderItemDto,
  ): OrderItem {
    const orderItem = new OrderItem();
    orderItem.order = order;
    orderItem.itemCode = createOrderItemDto.itemCode;
    orderItem.itemName = createOrderItemDto.itemName;
    orderItem.quantity = createOrderItemDto.quantity;
    orderItem.unitPrice = createOrderItemDto.unitPrice;
    orderItem.currency = createOrderItemDto.currency;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    orderItem.customizations = createOrderItemDto.customizations;
    orderItem.lineTotal = createOrderItemDto.lineTotal;

    return orderItem;
  }

  createOrderItems(
    order: Order,
    createOrderItemDtos: CreateOrderItemDto[],
  ): OrderItem[] {
    return createOrderItemDtos.map((itemDto) =>
      this.createOrderItem(order, itemDto),
    );
  }
}
