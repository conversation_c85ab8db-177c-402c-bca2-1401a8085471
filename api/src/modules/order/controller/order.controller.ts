import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';
import { CreateOrderRequestDto } from '../dto/create-order-request.dto';
import { UpdateOrderStatusDto } from '../dto/update-order-status.dto';
import { Order } from '../entity/order.entity';
import { OrderService } from '../service/order.service';

@Controller('orders')
@UseGuards(ApiKeyGuard)
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  async findAll(): Promise<Order[]> {
    return this.orderService.findAll();
  }

  @Get(':id')
  async findById(@Param('id') id: string): Promise<Order> {
    return this.orderService.findById(id);
  }

  @Post()
  async create(
    @Body() createOrderRequestDto: CreateOrderRequestDto,
  ): Promise<Order> {
    return this.orderService.create(createOrderRequestDto);
  }

  @Patch(':id')
  async updateOrder(
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderStatusDto,
  ): Promise<Order> {
    return this.orderService.updateStatus(id, updateOrderDto.orderStatus);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<void> {
    return this.orderService.delete(id);
  }
}
