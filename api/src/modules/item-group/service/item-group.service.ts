import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InsertResult, Repository } from 'typeorm';
import { ItemGroup } from '../entity/item-group.entity';
import { AssetsService } from 'src/modules/core/service/assets.service';

@Injectable()
export class ItemGroupService {
  constructor(
    @InjectRepository(ItemGroup)
    private readonly itemGroupRepository: Repository<ItemGroup>,
    private readonly assetsService: AssetsService,
  ) {}

  async createOrUpdateItemGroup(itemGroup: ItemGroup): Promise<InsertResult> {
    let previousLogoPath: string | undefined = undefined;
    if (itemGroup.image) {
      // If updating, get the previous logo path
      const existing = itemGroup.name
        ? await this.itemGroupRepository.findOne({
            where: { name: itemGroup.name },
          })
        : undefined;
      if (existing && existing.image && existing.image.startsWith('/assets/')) {
        previousLogoPath = existing.image;
      }
      const newLogoPath = await this.assetsService.handleAssetForDoctype({
        erpUrl: itemGroup.image,
        doctype: 'ItemGroup',
        doctypeId: itemGroup.name,
        assetField: 'image',
        previousLocalPath: previousLogoPath,
      });
      if (newLogoPath) {
        itemGroup.image = newLogoPath;
      }
    }
    return this.itemGroupRepository.upsert(itemGroup, {
      conflictPaths: ['name'],
      skipUpdateIfNoValuesChanged: true,
    });
  }

  async deleteItemGroup(name: string): Promise<void> {
    await this.itemGroupRepository.delete({ name });
  }

  async findAll(): Promise<ItemGroup[]> {
    return this.itemGroupRepository.find();
  }

  async findByName(name: string): Promise<ItemGroup> {
    const itemGroup = await this.itemGroupRepository.findOne({
      where: { name },
    });
    if (!itemGroup) {
      throw new NotFoundException('Item group not found');
    }
    return itemGroup;
  }

  async findChildren(parentName: string): Promise<ItemGroup[]> {
    return this.itemGroupRepository.find({
      where: { parent_item_group: parentName },
    });
  }

  async findByParent(parentName: string): Promise<ItemGroup[]> {
    return this.itemGroupRepository.find({
      where: { parent_item_group: parentName },
    });
  }
}
