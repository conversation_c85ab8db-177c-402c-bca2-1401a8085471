import {
  Controller,
  Get,
  Param,
  UseGuards,
  NotFoundException,
} from '@nestjs/common';
import { ModeOfPaymentService } from '../service/mode-of-payment.service';
import { ApiKeyGuard } from '../../auth/guard/api-key.guard';

@Controller('modes-of-payment')
@UseGuards(ApiKeyGuard)
export class ModeOfPaymentController {
  constructor(private readonly modeOfPaymentService: ModeOfPaymentService) {}

  @Get()
  async getModesOfPayment() {
    return this.modeOfPaymentService.findModeOfPayments();
  }

  @Get(':name')
  async getModeOfPaymentByName(@Param('name') name: string) {
    const modeOfPayment =
      await this.modeOfPaymentService.findModeOfPaymentByName(name);
    if (!modeOfPayment) {
      throw new NotFoundException(`Mode of payment '${name}' not found`);
    }
    return modeOfPayment;
  }
}
