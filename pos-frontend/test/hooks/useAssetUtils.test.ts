import { vi, beforeEach, describe, it, expect, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useAssetUtils } from '../../src/hooks/useAssetUtils';

describe('useAssetUtils', () => {
  let hook: ReturnType<typeof useAssetUtils>;

  beforeEach(() => {
    // Mock the environment variable for Vitest
    vi.stubEnv('VITE_API_URL', 'https://api.example.com');

    const { result } = renderHook(() => useAssetUtils());
    hook = result.current;
  });

  afterEach(() => {
    vi.unstubAllEnvs();
  });

  describe('getFullPath', () => {
    it('should return absolute URLs unchanged', () => {
      const absoluteUrls = [
        'https://example.com/image.jpg',
        'http://localhost:3000/asset.png',
        'https://cdn.example.com/files/document.pdf'
      ];

      absoluteUrls.forEach(url => {
        const result = hook.getFullPath(url);
        expect(result).toBe(url);
      });
    });

    it('should prepend API URL to relative paths', () => {
      const relativePaths = [
        '/images/logo.png',
        'assets/icons/menu.svg',
        '/uploads/documents/report.pdf'
      ];

      relativePaths.forEach(path => {
        const result = hook.getFullPath(path);
        expect(result).toBe('https://api.example.com' + path);
      });
    });

    it('should handle paths without leading slash', () => {
      const paths = [
        'images/logo.png',
        'assets/icons/menu.svg',
        'uploads/documents/report.pdf'
      ];

      paths.forEach(path => {
        const result = hook.getFullPath(path);
        expect(result).toBe('https://api.example.com' + path);
      });
    });

    it('should handle empty path', () => {
      const result = hook.getFullPath('');
      expect(result).toBe('https://api.example.com');
    });

    it('should handle path with only slash', () => {
      const result = hook.getFullPath('/');
      expect(result).toBe('https://api.example.com/');
    });

    it('should handle paths with query parameters', () => {
      const path = '/images/photo.jpg?size=large&format=webp';
      const result = hook.getFullPath(path);
      expect(result).toBe('https://api.example.com' + path);
    });

    it('should handle paths with hash fragments', () => {
      const path = '/documents/manual.pdf#page=5';
      const result = hook.getFullPath(path);
      expect(result).toBe('https://api.example.com' + path);
    });

    it('should handle complex URLs with multiple slashes', () => {
      const path = '/api/v1/assets/images/products/123/thumbnail.jpg';
      const result = hook.getFullPath(path);
      expect(result).toBe('https://api.example.com' + path);
    });

    it('should throw error when VITE_API_URL is not set', () => {
        vi.stubEnv('VITE_API_URL', '');

      expect(() => {
        hook.getFullPath('/images/logo.png');
      }).toThrow('VITE_API_URL is not set');
    });

    it('should throw error when VITE_API_URL is empty string', () => {
      // Set VITE_API_URL to empty string
      vi.unstubAllEnvs();
      vi.stubEnv('VITE_API_URL', '');

      // Re-render the hook with the new environment
      const { result } = renderHook(() => useAssetUtils());
      const newHook = result.current;

      expect(() => {
        newHook.getFullPath('/images/logo.png');
      }).toThrow('VITE_API_URL is not set');
    });

    it('should throw error when VITE_API_URL is undefined', () => {
      vi.stubEnv('VITE_API_URL', undefined);

      expect(() => {
        hook.getFullPath('/images/logo.png');
      }).toThrow('VITE_API_URL is not set');
    });

    it('should handle different API URL formats', () => {
      const testCases = [
        {
          apiUrl: 'https://api.example.com',
          path: '/images/logo.png',
          expected: 'https://api.example.com/images/logo.png'
        },
        {
          apiUrl: 'https://api.example.com/',
          path: '/images/logo.png',
          expected: 'https://api.example.com//images/logo.png'
        },
        {
          apiUrl: 'https://api.example.com',
          path: 'images/logo.png',
          expected: 'https://api.example.comimages/logo.png'
        },
        {
          apiUrl: 'https://api.example.com/',
          path: 'images/logo.png',
          expected: 'https://api.example.com/images/logo.png'
        }
      ];

      testCases.forEach(({ apiUrl, path, expected }) => {
        vi.unstubAllEnvs();
        vi.stubEnv('VITE_API_URL', apiUrl);

        const { result } = renderHook(() => useAssetUtils());
        const testHook = result.current;

        const resultPath = testHook.getFullPath(path);
        expect(resultPath).toBe(expected);
      });
    });

    it('should handle special characters in paths', () => {
      const specialPaths = [
        '/images/logo with spaces.png',
        '/assets/icons/menu-icon.svg',
        '/uploads/documents/report (2024).pdf',
        '/files/name-with-dashes.jpg',
        '/content/name_with_underscores.txt'
      ];

      specialPaths.forEach(path => {
        const result = hook.getFullPath(path);
        expect(result).toBe('https://api.example.com' + path);
      });
    });

    it('should handle international characters in paths', () => {
      const internationalPaths = [
        '/images/logo-中文.png',
        '/assets/icons/ícono-menú.svg',
        '/uploads/documents/rapport-été.pdf'
      ];

      internationalPaths.forEach(path => {
        const result = hook.getFullPath(path);
        expect(result).toBe('https://api.example.com' + path);
      });
    });
  });

  describe('hook structure', () => {
    it('should return an object with getFullPath function', () => {
      expect(typeof hook).toBe('object');
      expect(typeof hook.getFullPath).toBe('function');
    });

    it('should not have any other properties', () => {
      const keys = Object.keys(hook);
      expect(keys).toEqual(['getFullPath']);
    });
  });

  describe('edge cases', () => {
    it('should handle very long paths', () => {
      const longPath = '/a'.repeat(1000) + '/image.jpg';
      const result = hook.getFullPath(longPath);
      expect(result).toBe('https://api.example.com' + longPath);
    });

    it('should handle paths with only dots', () => {
      const path = '.../image.jpg';
      const result = hook.getFullPath(path);
      expect(result).toBe('https://api.example.com' + path);
    });

    it('should handle paths with only numbers', () => {
      const path = '/123/456/789.jpg';
      const result = hook.getFullPath(path);
      expect(result).toBe('https://api.example.com' + path);
    });
  });
}); 