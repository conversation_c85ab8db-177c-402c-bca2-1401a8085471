import { renderHook } from '@testing-library/react';
import { useQuery } from '@tanstack/react-query';
import { 
  usePosProfiles, 
  useSelectedPosProfile, 
  getSelectedPosProfileId, 
  setSelectedPosProfileId, 
  clearSelectedPosProfileId,
  getPaymentMethodsFromProfile
} from '../../src/hooks/pos-profile.hooks';
import { PosProfile } from '../../src/model/pos-profile.model';
import { vi, beforeEach, describe, it, expect } from 'vitest';

// Mock axios
vi.mock('axios');

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock useQuery to control the hook behavior
const mockUseQuery = useQuery as ReturnType<typeof vi.fn>;

const mockPosProfiles: PosProfile[] = [
  {
    name: 'Test Profile 1',
    company: 'Test Company 1',
    payments: [
      { mode_of_payment: 'Cash', default: 1 },
      { mode_of_payment: 'Credit Card', default: 0 }
    ]
  },
  {
    name: 'Test Profile 2',
    company: 'Test Company 2',
    payments: [
      { mode_of_payment: 'Debit Card', default: 1 }
    ]
  }
];

describe('POS Profile Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  describe('usePosProfiles', () => {
    it('should call useQuery with correct parameters', () => {
      vi.mocked(mockUseQuery).mockReturnValue({
        data: mockPosProfiles,
        isSuccess: true,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => usePosProfiles());

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["pos-profiles"],
        queryFn: expect.any(Function),
      });
    });
  });

  describe('useSelectedPosProfile', () => {
    it('should call useQuery with correct parameters when profile ID exists', () => {
      localStorageMock.getItem.mockReturnValue('Test Profile 1');
      
      vi.mocked(mockUseQuery).mockReturnValue({
        data: mockPosProfiles[0],
        isSuccess: true,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => useSelectedPosProfile());

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["selected-pos-profile", "Test Profile 1"],
        queryFn: expect.any(Function),
        enabled: true,
        refetchInterval: 5 * 60 * 1000,
      });
      expect(localStorageMock.getItem).toHaveBeenCalledWith('selectedPosProfileId');
    });

    it('should not enable query when no profile ID is stored', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      vi.mocked(mockUseQuery).mockReturnValue({
        data: undefined,
        isSuccess: false,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => useSelectedPosProfile());

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["selected-pos-profile", null],
        queryFn: expect.any(Function),
        enabled: false,
        refetchInterval: 5 * 60 * 1000,
      });
    });
  });

  describe('Local Storage Utilities', () => {
    it('should get selected profile ID from localStorage', () => {
      localStorageMock.getItem.mockReturnValue('Test Profile 1');
      
      const result = getSelectedPosProfileId();
      
      expect(result).toBe('Test Profile 1');
      expect(localStorageMock.getItem).toHaveBeenCalledWith('selectedPosProfileId');
    });

    it('should return null when no profile ID is stored', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const result = getSelectedPosProfileId();
      
      expect(result).toBeNull();
    });

    it('should set selected profile ID in localStorage', () => {
      setSelectedPosProfileId('Test Profile 1');
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('selectedPosProfileId', 'Test Profile 1');
    });

    it('should clear selected profile ID from localStorage', () => {
      clearSelectedPosProfileId();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('selectedPosProfileId');
    });
  });

  describe('Helper Functions', () => {
    it('should get payment methods from profile', () => {
      const result = getPaymentMethodsFromProfile(mockPosProfiles[0]);
      
      expect(result).toEqual([
        { mode_of_payment: 'Cash', default: 1 },
        { mode_of_payment: 'Credit Card', default: 0 }
      ]);
    });

    it('should return empty array when no payment methods', () => {
      const profileWithoutPayments: PosProfile = {
        name: 'Test Profile',
        payments: undefined
      };
      
      const result = getPaymentMethodsFromProfile(profileWithoutPayments);
      
      expect(result).toEqual([]);
    });
  });
});