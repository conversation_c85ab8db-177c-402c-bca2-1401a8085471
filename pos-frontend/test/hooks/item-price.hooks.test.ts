import { renderHook } from '@testing-library/react';
import { useQuery } from '@tanstack/react-query';
import { useItemPrices } from '../../src/hooks/item-price.hooks';
import { vi, beforeEach, describe, it, expect } from 'vitest';

// Mock axios
vi.mock('axios');

// Mock useQuery to control the hook behavior
const mockUseQuery = useQuery as ReturnType<typeof vi.fn>;

describe('Item Price Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useItemPrices', () => {
    it('should call useQuery with price_list parameter when provided', () => {
      const priceList = 'Standard Selling';
      
      vi.mocked(mockUseQuery).mockReturnValue({
        data: [],
        isSuccess: true,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => useItemPrices(priceList));

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["item-prices", priceList],
        queryFn: expect.any(Function),
        enabled: true,
      });
    });

    it('should not enable query when no price list is provided', () => {
      vi.mocked(mockUseQuery).mockReturnValue({
        data: undefined,
        isSuccess: false,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => useItemPrices());

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["item-prices", undefined],
        queryFn: expect.any(Function),
        enabled: false,
      });
    });

    it('should not enable query when empty price list is provided', () => {
      vi.mocked(mockUseQuery).mockReturnValue({
        data: undefined,
        isSuccess: false,
        isError: false,
        error: null,
        isLoading: false,
      } as ReturnType<typeof useQuery>);

      renderHook(() => useItemPrices(''));

      expect(mockUseQuery).toHaveBeenCalledWith({
        queryKey: ["item-prices", ''],
        queryFn: expect.any(Function),
        enabled: false,
      });
    });
  });
});