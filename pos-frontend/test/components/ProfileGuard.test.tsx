import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ReactNode } from 'react';
import ProfileGuard from '../../src/components/ProfileGuard';
import { getSelectedPosProfileId } from '../../src/hooks/pos-profile.hooks';
import { vi, beforeEach, describe, it, expect } from 'vitest';

// Mock the hooks
vi.mock('../../src/hooks/pos-profile.hooks', () => ({
  getSelectedPosProfileId: vi.fn(),
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
const mockLocation = { pathname: '/' };

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
  };
});

// Test wrapper
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('ProfileGuard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation.pathname = '/';
  });

  it('should render children when profile is selected', () => {
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue('Test Profile 1');

    render(
      <ProfileGuard>
        <div>Protected Content</div>
      </ProfileGuard>,
      { wrapper: TestWrapper }
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('should redirect to profile selection when no profile is selected', () => {
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue(null);

    render(
      <ProfileGuard>
        <div>Protected Content</div>
      </ProfileGuard>,
      { wrapper: TestWrapper }
    );

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    expect(mockNavigate).toHaveBeenCalledWith('/select-profile', { replace: true });
  });

  it('should not redirect when already on profile selection page', () => {
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue(null);
    mockLocation.pathname = '/select-profile';

    render(
      <ProfileGuard>
        <div>Profile Selection Content</div>
      </ProfileGuard>,
      { wrapper: TestWrapper }
    );

    expect(screen.getByText('Profile Selection Content')).toBeInTheDocument();
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('should render children when on profile selection page regardless of profile status', () => {
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue(null);
    mockLocation.pathname = '/select-profile';

    render(
      <ProfileGuard>
        <div>Profile Selection Content</div>
      </ProfileGuard>,
      { wrapper: TestWrapper }
    );

    expect(screen.getByText('Profile Selection Content')).toBeInTheDocument();
  });

  it('should handle profile state changes', () => {
    const { rerender } = render(
      <ProfileGuard>
        <div>Protected Content</div>
      </ProfileGuard>,
      { wrapper: TestWrapper }
    );

    // Initially no profile
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue(null);
    rerender(
      <ProfileGuard>
        <div>Protected Content</div>
      </ProfileGuard>
    );

    expect(mockNavigate).toHaveBeenCalledWith('/select-profile', { replace: true });

    // Profile gets selected
    (getSelectedPosProfileId as ReturnType<typeof vi.fn>).mockReturnValue('Test Profile 1');
    rerender(
      <ProfileGuard>
        <div>Protected Content</div>
      </ProfileGuard>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });
});