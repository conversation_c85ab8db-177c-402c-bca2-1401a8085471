import { vi, beforeEach, describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ReactNode } from 'react';
import PosProfileSelectionPage from '../../src/pages/PosProfileSelectionPage';
import { usePosProfiles, setSelectedPosProfileId } from '../../src/hooks/pos-profile.hooks';
import { PosProfile } from '../../src/model/pos-profile.model';

// Mock the hooks
vi.mock('../../src/hooks/pos-profile.hooks', () => ({
  usePosProfiles: vi.fn(),
  setSelectedPosProfileId: vi.fn(),
}));

// Mock react-router-dom navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => ({
  ...(await vi.importActual('react-router-dom')),
  useNavigate: () => mockNavigate,
}));

const mockPosProfiles: PosProfile[] = [
  {
    name: 'Test Profile 1',
    company: 'Test Company 1',
    payments: []
  },
  {
    name: 'Test Profile 2',
    company: 'Test Company 2',
    payments: []
  }
];

// Test wrapper
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <BrowserRouter>
    {children}
  </BrowserRouter>
);

describe('PosProfileSelectionPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should show loading state', () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    expect(screen.getByText('common.loading...')).toBeInTheDocument();
  });

  it('should show error state', () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Network error'),
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    expect(screen.getByText('common.error_loading_profiles')).toBeInTheDocument();
  });

  it('should show no profiles available state', () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: [],
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    expect(screen.getByText('common.no_profiles_available')).toBeInTheDocument();
  });

  it('should render profiles selection UI', () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: mockPosProfiles,
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    expect(screen.getByText('posProfile.select_pos_profile')).toBeInTheDocument();
    expect(screen.getByText('posProfile.select_pos_profile_description')).toBeInTheDocument();
    expect(screen.getByText('Test Profile 1')).toBeInTheDocument();
    expect(screen.getByText('Test Profile 2')).toBeInTheDocument();
    expect(screen.getByText('common.company: Test Company 1')).toBeInTheDocument();
    expect(screen.getByText('common.company: Test Company 2')).toBeInTheDocument();
  });

  it('should allow profile selection and enable continue button', async () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: mockPosProfiles,
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    const continueButton = screen.getByText('common.continue');
    expect(continueButton).toBeDisabled();

    // Click on profile 1
    const profile1Radio = screen.getByRole('radio', { name: 'Test Profile 1' });
    fireEvent.click(profile1Radio);

    expect(continueButton).toBeEnabled();
  });

  it('should save selected profile and navigate on continue', async () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: mockPosProfiles,
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    // Select profile 1
    const profile1Radio = screen.getByRole('radio', { name: 'Test Profile 1' });
    fireEvent.click(profile1Radio);

    // Click continue
    const continueButton = screen.getByText('common.continue');
    fireEvent.click(continueButton);

    expect(setSelectedPosProfileId).toHaveBeenCalledWith('Test Profile 1');
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('should allow clicking on profile label to select', async () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: mockPosProfiles,
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    // Click on profile 2 label
    const profile2Label = screen.getByText('Test Profile 2');
    fireEvent.click(profile2Label.closest('div')!);

    const continueButton = screen.getByText('common.continue');
    expect(continueButton).toBeEnabled();

    // Click continue
    fireEvent.click(continueButton);

    expect(setSelectedPosProfileId).toHaveBeenCalledWith('Test Profile 2');
    expect(mockNavigate).toHaveBeenCalledWith('/');
  });

  it('should not navigate if no profile is selected', () => {
    vi.mocked(usePosProfiles).mockReturnValue({
      data: mockPosProfiles,
      isLoading: false,
      error: null,
    });

    render(<PosProfileSelectionPage />, { wrapper: TestWrapper });

    const continueButton = screen.getByText('common.continue');
    expect(continueButton).toBeDisabled();

    // Try to click continue (should not work)
    fireEvent.click(continueButton);

    expect(setSelectedPosProfileId).not.toHaveBeenCalled();
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});