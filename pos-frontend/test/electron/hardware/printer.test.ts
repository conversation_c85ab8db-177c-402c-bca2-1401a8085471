import { vi, beforeEach, describe, it, expect } from 'vitest';

// Mock PrinterService for testing
class MockPrinterService {
  private isConnected: boolean = false;

  async print(data: { content?: string; copies?: number; [key: string]: unknown } | null) {
    console.log('[PrinterService] Print request received:', data);
    return {
      success: true,
      message: 'Print job queued successfully',
      jobId: `print_${Date.now()}`
    };
  }

  async getStatus() {
    console.log('[PrinterService] Status check requested');
    return {
      connected: this.isConnected,
      ready: true,
      paper: 'ok' as const,
      error: null
    };
  }

  async connect() {
    console.log('[PrinterService] Connecting to printer...');
    this.isConnected = true;
    return { success: true };
  }

  async disconnect() {
    console.log('[PrinterService] Disconnecting from printer...');
    this.isConnected = false;
    return { success: true };
  }
}

describe('PrinterService', () => {
  let printerService: MockPrinterService;

  beforeEach(() => {
    printerService = new MockPrinterService();
  });


  describe('print', () => {
    it('should return success response with job ID', async () => {
      const testData = { content: 'Test receipt', copies: 1 };
      const result = await printerService.print(testData);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message', 'Print job queued successfully');
      expect(result).toHaveProperty('jobId');
      expect(result.jobId).toMatch(/^print_\d+$/);
    });

    it('should handle empty print data', async () => {
      const result = await printerService.print(null);
      
      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('jobId');
    });

    it('should log print requests', async () => {
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
      const testData = { content: 'Test receipt' };
      
      await printerService.print(testData);
      
      expect(consoleSpy).toHaveBeenCalledWith('[PrinterService] Print request received:', testData);
      consoleSpy.mockRestore();
    });
  });

  describe('getStatus', () => {
    it('should return current status', async () => {
      const status = await printerService.getStatus();

      expect(status).toHaveProperty('connected');
      expect(status).toHaveProperty('ready', true);
      expect(status).toHaveProperty('paper', 'ok');
      expect(status).toHaveProperty('error', null);
    });

    it('should reflect connection state', async () => {
      await printerService.connect();
      const status = await printerService.getStatus();
      
      expect(status.connected).toBe(true);
    });
  });

  describe('connect', () => {
    it('should set isConnected to true', async () => {
      const result = await printerService.connect();
      
      expect(result).toEqual({ success: true });
    });
  });

  describe('disconnect', () => {
    it('should set isConnected to false', async () => {
      await printerService.connect();
      const result = await printerService.disconnect();
      
      expect(result).toEqual({ success: true });
    });
  });
});