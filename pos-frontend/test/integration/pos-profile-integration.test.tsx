import { vi, beforeEach, describe, it, expect } from 'vitest';
import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useSelectedPosProfile } from '../../src/hooks/pos-profile.hooks';
import { useItemPrices } from '../../src/hooks/item-price.hooks';
import { PosProfile } from '../../src/model/pos-profile.model';

// Mock the hooks
vi.mock('../../src/hooks/pos-profile.hooks');
vi.mock('../../src/hooks/item-price.hooks');

const mockUseSelectedPosProfile = vi.mocked(useSelectedPosProfile);
const mockUseItemPrices = vi.mocked(useItemPrices);

const mockPosProfile: PosProfile = {
  name: 'Test Restaurant Profile',
  company: 'Test Restaurant Inc',
  currency: 'USD',
  customer: 'Test Customer',
  warehouse: 'Main Warehouse',
  selling_price_list: 'Restaurant Prices',
  payments: [
    { mode_of_payment: 'Cash', default: 1 },
    { mode_of_payment: 'Credit Card', default: 0 }
  ]
};

// Test component that uses the POS profile data
const TestComponent = () => {
  const { data: selectedPosProfile } = useSelectedPosProfile();
  const { data: itemPrices } = useItemPrices(selectedPosProfile?.selling_price_list);

  if (!selectedPosProfile) {
    return <div>No profile selected</div>;
  }

  return (
    <div>
      <div data-testid="company">{selectedPosProfile.company}</div>
      <div data-testid="currency">{selectedPosProfile.currency}</div>
      <div data-testid="customer">{selectedPosProfile.customer}</div>
      <div data-testid="warehouse">{selectedPosProfile.warehouse}</div>
      <div data-testid="price-list">{selectedPosProfile.selling_price_list}</div>
      <div data-testid="payment-methods">
        {selectedPosProfile.payments?.map(p => p.mode_of_payment).join(', ')}
      </div>
      <div data-testid="prices-loaded">{itemPrices ? 'true' : 'false'}</div>
    </div>
  );
};

const renderWithQueryClient = (component: React.ReactElement) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('POS Profile Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should use selected POS profile as source of truth for all data', () => {
    // Mock the selected POS profile hook
    mockUseSelectedPosProfile.mockReturnValue({
      data: mockPosProfile,
      isLoading: false,
      isError: false,
      error: null,
    });

    // Mock the item prices hook to verify it's called with the correct price list
    mockUseItemPrices.mockReturnValue({
      data: [{ item_code: 'TEST_ITEM', price_list_rate: 10.99 }],
      isLoading: false,
      isError: false,
      error: null,
    });

    renderWithQueryClient(<TestComponent />);

    // Verify that all POS profile data is correctly displayed
    expect(screen.getByTestId('company')).toHaveTextContent('Test Restaurant Inc');
    expect(screen.getByTestId('currency')).toHaveTextContent('USD');
    expect(screen.getByTestId('customer')).toHaveTextContent('Test Customer');
    expect(screen.getByTestId('warehouse')).toHaveTextContent('Main Warehouse');
    expect(screen.getByTestId('price-list')).toHaveTextContent('Restaurant Prices');
    expect(screen.getByTestId('payment-methods')).toHaveTextContent('Cash, Credit Card');
    expect(screen.getByTestId('prices-loaded')).toHaveTextContent('true');

    // Verify that useItemPrices was called with the selling_price_list
    expect(mockUseItemPrices).toHaveBeenCalledWith('Restaurant Prices');
  });

  it('should handle missing POS profile gracefully', () => {
    // Mock no selected profile
    mockUseSelectedPosProfile.mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: false,
      error: null,
    });

    mockUseItemPrices.mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: false,
      error: null,
    });

    renderWithQueryClient(<TestComponent />);

    expect(screen.getByText('No profile selected')).toBeInTheDocument();
    
    // Verify that useItemPrices was called with undefined
    expect(mockUseItemPrices).toHaveBeenCalledWith(undefined);
  });

  it('should pass payment methods from POS profile', () => {
    const profileWithDifferentPayments: PosProfile = {
      ...mockPosProfile,
      payments: [
        { mode_of_payment: 'Debit Card', default: 1 },
        { mode_of_payment: 'Mobile Payment', default: 0 },
        { mode_of_payment: 'Check', default: 0 }
      ]
    };

    mockUseSelectedPosProfile.mockReturnValue({
      data: profileWithDifferentPayments,
      isLoading: false,
      isError: false,
      error: null,
    });

    mockUseItemPrices.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
      error: null,
    });

    renderWithQueryClient(<TestComponent />);

    expect(screen.getByTestId('payment-methods')).toHaveTextContent('Debit Card, Mobile Payment, Check');
  });
});