{"name": "saba-pos-frontend", "private": true, "version": "0.0.0", "type": "module", "main": "electron/main.js", "homepage": "./", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint:fix": "eslint . --fix", "lint": "eslint .", "electron": "NODE_ENV=development electron .", "electron:build": "npm run build && electron-builder --win", "electron:build:cashier": "cross-env VITE_APP_MODE=cashier npm run build && cross-env VITE_APP_MODE=cashier electron-builder --win", "electron:build:self-ordering": "cross-env VITE_APP_MODE=self-ordering npm run build && cross-env VITE_APP_MODE=self-ordering electron-builder --win", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.2", "@tanstack/react-query": "^5.79.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "i18next": "^24.2.3", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-router-dom": "^7.6.1", "react-world-flags": "^1.6.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.6", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.2", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.21.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/electron": "^1.4.38", "@types/node": "^22.14.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-world-flags": "^1.6.0", "@types/swiper": "^5.4.3", "@vitejs/plugin-react-swc": "^3.8.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "cross-env": "^7.0.3", "electron": "^37.2.3", "electron-builder": "^26.0.12", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jsdom": "^26.1.0", "tw-animate-css": "^1.3.3", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vitest": "^3.2.4"}, "build": {"appId": "com.saba.pos-frontend", "productName": "Saba POS", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "zip", "arch": ["x64"]}], "icon": "public/icon.ico"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "snap", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "${productName}"}, "extraMetadata": {"name": "saba-pos-frontend"}}}