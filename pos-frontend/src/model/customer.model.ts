export interface Customer {
  name: string;
  customer_name?: string;
  customer_type?: string;
  customer_pos_id?: string;
  language?: string;
  email_id?: string;
  mobile_no?: string;
  disabled?: number;
  customer_group?: string;
  territory?: string;
  market_segment?: string;
  default_price_list?: string;
  default_currency?: string;
  tax_category?: string;
  tax_id?: string;
  payment_terms?: string;
}