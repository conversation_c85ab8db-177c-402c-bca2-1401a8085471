import { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { getSelectedPosProfileId } from "../hooks/pos-profile.hooks";

interface ProfileGuardProps {
  children: React.ReactNode;
}

const ProfileGuard = ({ children }: ProfileGuardProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const selectedProfileId = getSelectedPosProfileId();

  useEffect(() => {
    // Don't redirect if already on profile selection page
    if (location.pathname === "/select-profile") {
      return;
    }

    // If no profile is selected, redirect to profile selection
    if (!selectedProfileId) {
      navigate("/select-profile", { replace: true });
    }
  }, [selectedProfileId, navigate, location.pathname]);

  // If no profile is selected and not on profile selection page, don't render children
  if (!selectedProfileId && location.pathname !== "/select-profile") {
    return null;
  }

  return <>{children}</>;
};

export default ProfileGuard;