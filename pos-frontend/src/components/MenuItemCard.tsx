import { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Item } from '../model/item.model';
import { ItemPrice } from '../model/item-price.model';
import { useCartStore } from '../stores/cartStore';
import { Button } from './ui/button';
import { usePriceFormat } from '../hooks/usePriceFormat';
import { useAssetUtils } from '@/hooks/useAssetUtils';

interface MenuItemCardProps {
  item: Item;
  priceObj?: ItemPrice;
}

const MenuItemCard = ({ item, priceObj }: MenuItemCardProps) => {
  const { t } = useTranslation();
  const { formatPrice } = usePriceFormat();
  const [quantity, setQuantity] = useState(1);
  const addItem = useCartStore(state => state.addItem);
  const { getFullPath } = useAssetUtils();

  // Memoize price calculations for better performance
  const priceInfo = useMemo(() => ({
    unitPrice: priceObj?.price_list_rate || 0,
    currency: priceObj?.currency || '',
    totalPrice: (priceObj?.price_list_rate || 0) * quantity,
    isAvailable: priceObj?.price_list_rate !== undefined
  }), [priceObj, quantity]);

  const handleQuantityChange = useCallback((delta: number) => {
    const newQuantity = quantity + delta;
    if (newQuantity > 0) {
      setQuantity(newQuantity);
    }
  }, [quantity]);

  const handleAddToCart = useCallback(() => {
    if (!priceInfo.isAvailable) return;
    addItem(item, quantity, [], priceObj);
    setQuantity(1); // Reset quantity after adding
  }, [item, quantity, priceObj, addItem, priceInfo.isAvailable]);

  return (
    <div className="bg-card rounded-lg p-6 shadow-md hover:shadow-lg transition-all duration-200 border border-border">
      {item.image && (
        <div className="w-full h-48 mb-4 bg-muted rounded-lg overflow-hidden">
          <img
            src={getFullPath(item.image)}
            alt={item.item_name || 'Menu item'}
            className="w-full h-full object-cover"
            loading="lazy"
          />
        </div>
      )}
      
      <div className="space-y-4">
        <h3 className="text-xl font-semibold text-card-foreground line-clamp-2 min-h-[3.5rem]">
          {item.item_name}
        </h3>
        
        {item.description && (
          <p className="text-muted-foreground text-sm line-clamp-3 min-h-[4.5rem]">
            {item.description}
          </p>
        )}
        
        <div className="space-y-2">
          <div className="text-lg font-medium text-muted-foreground">
            {priceInfo.isAvailable ? `${formatPrice(priceInfo.unitPrice, priceInfo.currency)} ${t('cart.each')}` : t('menu.priceNotAvailable')}
          </div>
          <div className="text-2xl font-bold text-primary">
            {priceInfo.isAvailable ? formatPrice(priceInfo.totalPrice, priceInfo.currency) : '—'}
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-center space-x-3">
            <Button
              variant="secondary"
              size="icon"
              onClick={() => handleQuantityChange(-1)}
              disabled={quantity <= 1}
              className="h-10 w-10"
            >
              -
            </Button>
            
            <span className="text-xl font-semibold text-card-foreground min-w-[2rem] text-center">
              {quantity}
            </span>
            
            <Button
              variant="secondary"
              size="icon"
              onClick={() => handleQuantityChange(1)}
              className="h-10 w-10"
            >
              +
            </Button>
          </div>
          
          <Button
            onClick={handleAddToCart}
            disabled={!priceInfo.isAvailable}
            className="w-full"
            size="lg"
          >
            {t('menu.addToCart')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MenuItemCard;