import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useCartStore } from "../stores/cartStore";
import { useSelectedPosProfile } from "../hooks/pos-profile.hooks";
import { Button } from "./ui/button";
import { usePriceFormat } from "../hooks/usePriceFormat";

const Footer = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { total, getItemCount } = useCartStore();
  const { data: selectedPosProfile } = useSelectedPosProfile();
  const { formatPrice } = usePriceFormat();
  
  const currency = selectedPosProfile?.currency || '';
  const itemCount = getItemCount();

  const handleViewCart = () => {
    navigate('/order-summary');
  };

  return (
    <footer
      className="w-full bg-muted border-t border-muted py-3 px-6 shadow-inner text-sm text-muted-foreground mt-auto"
      role="contentinfo"
      aria-label="Footer"
      tabIndex={0}
    >
      <div className="flex items-center justify-between">
        <span>{t("footer")}</span>
        
        {itemCount > 0 && (
          <div className="flex items-center space-x-4">
            <div className="text-sm">
              <span className="font-medium">
                {itemCount} {itemCount === 1 ? t('cart.item') : t('cart.items')}
              </span>
              <span className="ml-2 font-semibold text-primary">
                {formatPrice(total, currency)}
              </span>
            </div>
            <Button 
              size="sm" 
              onClick={handleViewCart}
              className="bg-primary text-primary-foreground hover:bg-primary/90"
            >
              {t('cart.viewCart')}
            </Button>
          </div>
        )}
      </div>
    </footer>
  );
};

export default Footer; 