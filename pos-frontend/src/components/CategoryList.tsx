import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ItemGroup } from '../model/item-group.model';
import { useItemGroupChildren } from '../hooks/item-group.hooks';
import { useAssetUtils } from '@/hooks/useAssetUtils';

interface CategoryListProps {
  categories: ItemGroup[];
  onCategorySelect: (categoryId: string | null) => void;
}

const CategoryList = ({ categories, onCategorySelect }: CategoryListProps) => {
  const { t } = useTranslation();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedGroup, setExpandedGroup] = useState<string | null>(null);
  const { getFullPath } = useAssetUtils();
  // Only call the hook for the expanded group
  const { data: expandedChildren } = useItemGroupChildren(expandedGroup || '');

  const handleCategoryClick = (itemGroup: ItemGroup) => {
    if (itemGroup.is_group === 1) {
      // This is a group - expand/collapse it
      setExpandedGroup(expandedGroup === itemGroup.name ? null : itemGroup.name);
    } else {
      // This is a leaf group - select it for items
      setSelectedCategory(itemGroup.name);
      onCategorySelect(itemGroup.name);
    }
  };

  const getHighlightStyle = (itemGroup: ItemGroup) => {
    return selectedCategory === itemGroup.name && itemGroup.is_group === 0
      ? 'bg-primary text-primary-foreground'
      : 'bg-secondary hover:bg-secondary/80 text-secondary-foreground';
  };

  return (
    <div className="bg-card rounded-lg p-4 shadow-md">
      <h2 className="text-xl font-semibold text-card-foreground mb-4">
        {t('menu.categories')}
      </h2>
      
      <div className="space-y-2">
        {categories.map((category) => (
          <div key={category.name}>
            <button
              onClick={() => handleCategoryClick(category)}
              className={`w-full text-left p-3 rounded-lg transition-colors font-medium touch-manipulation ${getHighlightStyle(category)}`}
            >
              <div className="flex items-center space-x-3">
                {category.image && (
                  <div className="w-8 h-8 bg-muted rounded overflow-hidden flex-shrink-0">
                    <img
                      src={getFullPath(category.image)}
                      alt={category.item_group_name || 'Category'}
                      className="w-full h-full object-cover"
                      loading="lazy"
                    />
                  </div>
                )}
                
                <span className="truncate flex-1">
                  {category.item_group_name || category.name}
                </span>
                
                {category.is_group === 1 && (
                  <span className="text-xs opacity-70">
                    {expandedGroup === category.name ? '▼' : '▶'}
                  </span>
                )}
              </div>
            </button>
            
            {/* Sub-categories */}
            {expandedGroup === category.name && category.is_group === 1 && (
              <div className="ml-4 mt-2 space-y-2">
                {expandedChildren?.map((child) => (
                  <button
                    key={child.name}
                    onClick={() => handleCategoryClick(child)}
                    className={`w-full text-left p-3 rounded-lg transition-colors font-medium touch-manipulation ${getHighlightStyle(child)}`}
                  >
                    <div className="flex items-center space-x-3">
                      {child.image && (
                        <div className="w-6 h-6 bg-muted rounded overflow-hidden flex-shrink-0">
                          <img
                            src={getFullPath(child.image)}
                            alt={child.item_group_name || 'Category'}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        </div>
                      )}
                      
                      <span className="truncate text-sm">
                        {child.item_group_name || child.name}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default CategoryList;