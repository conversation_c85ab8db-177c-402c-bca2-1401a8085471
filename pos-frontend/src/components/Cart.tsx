import { useTranslation } from 'react-i18next';
import { useCartStore } from '../stores/cartStore';
import { useSelectedPosProfile } from '../hooks/pos-profile.hooks';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { usePriceFormat } from '../hooks/usePriceFormat';

const Cart = () => {
  const { t } = useTranslation();
  const { items, total, updateQuantity, removeItem, getItemCount } = useCartStore();
  const { data: selectedPosProfile } = useSelectedPosProfile();
  const { formatPrice } = usePriceFormat();

  if (items.length === 0) {
    return (
      <div className="bg-card rounded-lg p-4 shadow-md">
        <h3 className="text-lg font-semibold text-card-foreground mb-3">
          {t('cart.title')}
        </h3>
        <p className="text-muted-foreground text-center py-8">
          {t('cart.empty')}
        </p>
      </div>
    );
  }

  return (
    <div className="bg-card rounded-lg p-4 shadow-md">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-card-foreground">
          {t('cart.title')}
        </h3>
        <Badge variant="default">
          {getItemCount()}
        </Badge>
      </div>

      <div className="space-y-3 max-h-64 overflow-y-auto">
        {items.map((cartItem) => {
          const price = cartItem.priceObj?.price_list_rate || 0;
          const currency = cartItem.priceObj?.currency;
          
          return (
            <div
              key={cartItem.id}
              className="bg-background rounded-lg p-3 border border-border"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-card-foreground truncate">
                    {cartItem.item.item_name}
                  </h4>
                  <p className="text-sm text-muted-foreground">
                    {formatPrice(price, currency)} {t('cart.each')}
                  </p>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeItem(cartItem.id)}
                  className="text-destructive hover:text-destructive/80 ml-2 h-6 w-6 p-0"
                  aria-label={t('cart.remove')}
                >
                  ✕
                </Button>
              </div>
              
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => updateQuantity(cartItem.id, cartItem.quantity - 1)}
                    disabled={cartItem.quantity <= 1}
                    className="h-8 w-8 p-0"
                  >
                    -
                  </Button>
                  
                  <span className="text-sm font-medium w-8 text-center">
                    {cartItem.quantity}
                  </span>
                  
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={() => updateQuantity(cartItem.id, cartItem.quantity + 1)}
                    className="h-8 w-8 p-0"
                  >
                    +
                  </Button>
                </div>
                
                <div className="font-semibold text-primary">
                  {formatPrice(cartItem.subtotal, currency)}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <Separator className="mt-4" />
      <div className="pt-4">
        <div className="flex items-center justify-between">
          <span className="text-lg font-semibold text-card-foreground">
            {t('cart.total')}
          </span>
          <span className="text-xl font-bold text-primary">
            {formatPrice(total, selectedPosProfile?.currency || '')}
          </span>
        </div>
      </div>
    </div>
  );
};

export default Cart;