import { create } from 'zustand';

export type ApplicationMode = 'self-ordering' | 'cashier';

interface ApplicationModeState {
  mode: ApplicationMode;
  setMode: (mode: ApplicationMode) => void;
  isSelfOrdering: () => boolean;
  isCashierMode: () => boolean;
}

const getInitialMode = (): ApplicationMode => {
  const envMode = import.meta.env.VITE_APP_MODE as ApplicationMode;
  return envMode === 'self-ordering' || envMode === 'cashier' ? envMode : 'self-ordering';
};

export const useApplicationModeStore = create<ApplicationModeState>((set, get) => ({
  mode: getInitialMode(),
  setMode: (mode) => set({ mode }),
  isSelfOrdering: () => get().mode === 'self-ordering',
  isCashierMode: () => get().mode === 'cashier',
}));
