import React, { useState } from 'react';
import paymentService from '../lib/PaymentService';
import { useNavigate } from 'react-router-dom';
import { useCartStore } from '../stores/cartStore';
import { usePriceFormat } from '../hooks/usePriceFormat';
import { useCreateOrder, transformCartToOrderPayload, CreateOrderPayment } from '../hooks/order.hooks';
import { useSelectedPosProfile, getPaymentMethodsFromProfile } from '../hooks/pos-profile.hooks';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { Separator } from '../components/ui/separator';
import { Input } from '../components/ui/input';
import { useTranslation } from 'react-i18next';
import { CreditCard, AlertCircle, CheckCircle, Loader2, ArrowLeft, Plus, Trash2 } from 'lucide-react';
import { useApplicationModeStore } from '../stores/applicationModeStore';

const PaymentPage: React.FC = () => {
  const { t } = useTranslation();
  const { formatPrice } = usePriceFormat();
  const { total, items, clearCart } = useCartStore();
  const createOrder = useCreateOrder();
  const { data: selectedPosProfile, isLoading: isLoadingProfile } = useSelectedPosProfile();
  const { isSelfOrdering, isCashierMode } = useApplicationModeStore();
  const amount = total;
  const currency = selectedPosProfile?.currency || '';
  const [status, setStatus] = useState<'idle' | 'processing' | 'success' | 'error' | 'declined'>('idle');
  const [message, setMessage] = useState('');
  const [payments, setPayments] = useState<CreateOrderPayment[]>([
    {
      amount: total,
      currency,
      method: '',
      status: 'Completed',
      transactionReference: ''
    }
  ]);
  const navigate = useNavigate();

  // Get current POS profile and its payment methods
  const availablePaymentMethods = selectedPosProfile ? getPaymentMethodsFromProfile(selectedPosProfile) : [];

  // Helper functions for payment management
  const addPayment = () => {
    // In self-ordering mode, only allow one payment
    if (isSelfOrdering()) {
      return;
    }
    
    const remainingAmount = total - payments.reduce((sum, p) => sum + p.amount, 0);
    setPayments([...payments, {
      amount: remainingAmount > 0 ? remainingAmount : 0,
      currency,
      method: '',
      status: 'Completed',
      transactionReference: ''
    }]);
  };

  const removePayment = (index: number) => {
    // In self-ordering mode, don't allow removing the only payment
    if (isSelfOrdering() && payments.length === 1) {
      return;
    }
    
    if (payments.length > 1) {
      setPayments(payments.filter((_, i) => i !== index));
    }
  };

  const updatePayment = (index: number, field: keyof CreateOrderPayment, value: string | number) => {
    const updatedPayments = [...payments];
    updatedPayments[index] = { ...updatedPayments[index], [field]: value };
    setPayments(updatedPayments);
  };

  const totalPaymentAmount = payments.reduce((sum, p) => sum + p.amount, 0);
  const isPaymentValid = isSelfOrdering() 
    ? payments[0]?.method && payments[0]?.amount > 0  // In self-ordering, just need method selected
    : payments.every(p => p.method && p.amount > 0) && Math.abs(totalPaymentAmount - total) < 0.01;

  // Show loading state while profile is loading
  if (isLoadingProfile) {
    return (
      <div className="bg-background min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
          <p className="text-lg">{t('common.loading')}</p>
        </div>
      </div>
    );
  }



  const handlePayment = async (simulateSuccess?: boolean) => {
    // Validate payments
    if (!isPaymentValid) {
      setStatus('error');
      setMessage(isSelfOrdering() 
        ? 'Please select a payment method' 
        : 'Please ensure all payment methods are selected and amounts add up to the total'
      );
      return;
    }

    // Validate payment total matches order total (only for cashier mode)
    if (isCashierMode() && Math.abs(totalPaymentAmount - total) >= 0.01) {
      setStatus('error');
      setMessage(`Payment total (${formatPrice(totalPaymentAmount, currency)}) must equal order total (${formatPrice(total, currency)})`);
      return;
    }

    setStatus('processing');
    setMessage('');
    try {
      const result = await paymentService.startPayment(amount, simulateSuccess);
      if (result.success) {
        setStatus('success');
        setMessage(result.message);
        
        // Create order after successful payment
        try {
          if (!selectedPosProfile) {
            throw new Error('No POS profile selected');
          }
          
          if (!selectedPosProfile.customer) {
            throw new Error('No customer configured in selected POS profile');
          }
          
          const customer = selectedPosProfile.customer;
          
          // Generate unique transaction references for each payment
          const paymentsWithRefs = payments.map((payment, index) => ({
            ...payment,
            transactionReference: `PAY-${Date.now()}-${index}-${Math.random().toString(36).substring(2, 11)}`
          }));
          
          const orderPayload = transformCartToOrderPayload(items, total, paymentsWithRefs, customer, selectedPosProfile.name, currency);
          
          const orderResponse = await createOrder.mutateAsync(orderPayload);
          
          setTimeout(() => {
            clearCart();
            navigate(`/order-confirmation/${orderResponse.localId}`);
          }, 2000);
        } catch (orderError) {
          console.error('Failed to create order:', orderError);
          setStatus('error');
          const errorMessage = orderError instanceof Error ? orderError.message : 'Payment successful but failed to create order. Please contact support.';
          setMessage(errorMessage);
        }
      } else {
        setStatus('declined');
        setMessage(result.message);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred.';
      setStatus('error');
      setMessage(errorMessage);
    }
  };

  const handleBack = () => {
    navigate('/order-summary');
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'error':
      case 'declined':
        return <AlertCircle className="h-8 w-8 text-destructive" />;
      default:
        return <CreditCard className="h-8 w-8 text-muted-foreground" />;
    }
  };

  const getStatusMessage = () => {
    switch (status) {
      case 'processing':
        return t('payment.processing');
      case 'success':
        return t('payment.approved');
      case 'declined':
        return t('payment.declined');
      case 'error':
        return t('payment.error');
      default:
        return t('payment.ready');
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'declined':
      case 'error':
        return 'text-destructive';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <div className="bg-background min-h-screen">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <Button
            variant="secondary"
            onClick={handleBack}
            className="flex items-center gap-2"
            disabled={status === 'processing'}
          >
            <ArrowLeft className="h-4 w-4" />
            {t('common.back')}
          </Button>
          <h1 className="text-2xl font-bold text-foreground">{t('payment.title')}</h1>
          <div className="w-20" /> {/* Spacer for centering */}
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-2xl mx-auto p-6 space-y-6">
        {/* Payment Amount Card */}
        <Card className="p-8 text-center">
          <div className="space-y-4">
            <div className="flex justify-center">
              {getStatusIcon()}
            </div>
            <div>
              <p className="text-sm text-muted-foreground mb-2">{t('payment.amountDue')}</p>
              <p className="text-4xl font-bold text-foreground">
                {formatPrice(amount, currency)}
              </p>
            </div>
            <div className={`text-lg font-medium ${getStatusColor()}`}>
              {getStatusMessage()}
            </div>
            {message && (
              <div className="mt-4 p-3 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">{message}</p>
              </div>
            )}
          </div>
        </Card>

        {/* Order Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">{t('payment.orderSummary')}</h3>
          <div className="space-y-2">
            {items.map((item, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span>{item.quantity}× {item.item.name}</span>
                <span>{formatPrice(item.subtotal, currency)}</span>
              </div>
            ))}
            <Separator className="my-3" />
            <div className="flex justify-between font-semibold">
              <span>{t('cart.total')}</span>
              <span>{formatPrice(total, currency)}</span>
            </div>
          </div>
        </Card>

        {/* Payment Methods Section */}
        <Card className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">
              {isSelfOrdering() ? t('payment.selectMethod') : t('payment.paymentMethods')}
            </h3>

            {isSelfOrdering() ? (
              /* Self-Ordering Mode: Simple payment method selection */
              <div className="space-y-3">
                {availablePaymentMethods.length > 0 ? (
                  availablePaymentMethods.map((method, index) => (
                    <Button
                      key={method.mode_of_payment || index}
                      variant={payments[0]?.method === method.mode_of_payment ? "default" : "outline"}
                      className="w-full h-16 text-left justify-start text-lg"
                      onClick={() => updatePayment(0, 'method', method.mode_of_payment || '')}
                      disabled={status === 'processing'}
                    >
                      <CreditCard className="h-6 w-6 mr-3" />
                      <div className="flex flex-col items-start">
                        <span className="font-medium">{method.mode_of_payment}</span>
                        {method.default === 1 && (
                          <span className="text-xs text-muted-foreground">Recommended</span>
                        )}
                      </div>
                    </Button>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted" />
                    <p>No payment methods available</p>
                  </div>
                )}
              </div>
            ) : (
              /* Cashier Mode: Multiple payments with inputs */
              <>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Multiple payments allowed</span>
                  <Button
                    onClick={addPayment}
                    variant="outline"
                    size="sm"
                    disabled={status === 'processing'}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    {t('payment.addPayment')}
                  </Button>
                </div>

                <div className="space-y-4">
                  {payments.map((payment, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">Payment {index + 1}</span>
                        {payments.length > 1 && (
                          <Button
                            onClick={() => removePayment(index)}
                            variant="outline"
                            size="sm"
                            disabled={status === 'processing'}
                            className="text-destructive hover:text-destructive/80"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium mb-1">Amount</label>
                          <Input
                            type="number"
                            step="0.01"
                            value={payment.amount}
                            onChange={(e) => updatePayment(index, 'amount', parseFloat(e.target.value) || 0)}
                            disabled={status === 'processing'}
                            className="w-full"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">Payment Method</label>
                          <select
                            value={payment.method}
                            onChange={(e) => updatePayment(index, 'method', e.target.value)}
                            disabled={status === 'processing'}
                            className="w-full h-10 px-3 border border-gray-300 rounded-md"
                          >
                            <option value="">Select method</option>
                            {availablePaymentMethods.map((method) => (
                              <option key={method.mode_of_payment} value={method.mode_of_payment}>
                                {method.mode_of_payment}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center text-lg font-semibold">
                    <span>Payment Total:</span>
                    <span className={totalPaymentAmount === total ? 'text-green-600' : 'text-destructive'}>
                      {formatPrice(totalPaymentAmount, currency)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center text-sm text-muted-foreground">
                    <span>Order Total:</span>
                    <span>{formatPrice(total, currency)}</span>
                  </div>
                  {Math.abs(totalPaymentAmount - total) >= 0.01 && (
                    <div className="mt-2 text-sm text-destructive">
                      Difference: {formatPrice(Math.abs(totalPaymentAmount - total), currency)}
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </Card>

        {/* Payment Actions */}
        <Card className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-center">{t('payment.selectAction')}</h3>
            
            {status === 'idle' && (
              <div className="space-y-3">
                <Button 
                  onClick={() => handlePayment(true)} 
                  className="w-full h-14 text-lg bg-green-600 hover:bg-green-700"
                  disabled={!isPaymentValid}
                >
                  <CreditCard className="h-5 w-5 mr-2" />
                  {t('payment.simulateSuccess')}
                </Button>
                <Button 
                  onClick={() => handlePayment(false)} 
                  variant="outline"
                  className="w-full h-14 text-lg border-destructive text-destructive hover:bg-destructive/10"
                  disabled={!isPaymentValid}
                >
                  <AlertCircle className="h-5 w-5 mr-2" />
                  {t('payment.simulateFailure')}
                </Button>
              </div>
            )}

            {status === 'processing' && (
              <div className="text-center py-8">
                <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
                <p className="text-lg font-medium text-blue-600">{t('payment.processing')}</p>
                <p className="text-sm text-muted-foreground mt-2">{t('payment.pleaseWait')}</p>
              </div>
            )}

            {status === 'success' && (
              <div className="text-center py-8">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <p className="text-xl font-semibold text-green-600 mb-2">{t('payment.approved')}</p>
                <p className="text-sm text-muted-foreground">{t('payment.redirecting')}</p>
              </div>
            )}

            {(status === 'declined' || status === 'error') && (
              <div className="space-y-4">
                <div className="text-center py-4">
                  <AlertCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
                  <p className="text-lg font-medium text-destructive mb-2">
                    {status === 'declined' ? t('payment.declined') : t('payment.error')}
                  </p>
                  <p className="text-sm text-muted-foreground">{message}</p>
                </div>
                <Button 
                  onClick={() => handlePayment(true)}
                  className="w-full h-14 text-lg bg-blue-600 hover:bg-blue-700"
                >
                  {t('payment.retry')}
                </Button>
                <Button 
                  onClick={handleBack}
                  variant="outline"
                  className="w-full h-12"
                >
                  {t('payment.backToCart')}
                </Button>
              </div>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PaymentPage;