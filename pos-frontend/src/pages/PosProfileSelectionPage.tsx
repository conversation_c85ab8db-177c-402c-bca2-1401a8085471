import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { usePosProfiles, setSelectedPosProfileId } from "../hooks/pos-profile.hooks";
import { PosProfile } from "../model/pos-profile.model";
import { Button } from "../components/ui/button";
import { Card } from "../components/ui/card";
import { RadioGroup, RadioGroupItem } from "../components/ui/radio-group";
import { useTranslation } from "react-i18next";

const PosProfileSelectionPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { data: posProfiles, isLoading, error } = usePosProfiles();
    const [selectedProfileId, setSelectedProfileId] = useState<string>("");

    const handleSelectProfile = () => {
        if (selectedProfileId) {
            setSelectedPosProfileId(selectedProfileId);
            navigate("/");
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="text-lg text-foreground">{t("common.loading")}...</div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="text-destructive text-lg">{t("common.error_loading_profiles")}</div>
            </div>
        );
    }

    if (!posProfiles || posProfiles.length === 0) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-background">
                <div className="text-lg text-foreground">{t("common.no_profiles_available")}</div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background">
            <div className="container mx-auto px-4 py-8 max-w-2xl">
            <div className="text-center mb-8">
                <h1 className="text-3xl font-bold text-foreground mb-2">
                    {t("posProfile.select_pos_profile")}
                </h1>
                <p className="text-muted-foreground">
                    {t("posProfile.select_pos_profile_description")}
                </p>
            </div>

            <Card className="p-6">
                <RadioGroup
                    value={selectedProfileId}
                    onValueChange={setSelectedProfileId}
                    className="space-y-4"
                >
                    {posProfiles.map((profile: PosProfile) => (
                        <div
                            key={profile.name}
                            className="flex items-center space-x-3 p-4 border border-border rounded-lg hover:bg-accent cursor-pointer"
                            onClick={() => setSelectedProfileId(profile.name)}
                        >
                            <RadioGroupItem value={profile.name} id={profile.name} />
                            <div className="flex-1">
                                <label
                                    htmlFor={profile.name}
                                    className="text-lg font-medium cursor-pointer text-foreground"
                                >
                                    {profile.name}
                                </label>
                                {profile.company && (
                                    <p className="text-sm text-muted-foreground">
                                        {t("common.company")}: {profile.company}
                                    </p>
                                )}
                            </div>
                        </div>
                    ))}
                </RadioGroup>

                <div className="mt-8 flex justify-end">
                    <Button
                        onClick={handleSelectProfile}
                        disabled={!selectedProfileId}
                        className="px-6 py-2"
                    >
                        {t("common.continue")}
                    </Button>
                </div>
            </Card>
            </div>
        </div>
    );
};

export default PosProfileSelectionPage;