import Layout from "./Layout";
import WelcomePage from "./pages/WelcomePage";
import MenuBrowsePage from "./pages/MenuBrowsePage";
import OrderSummaryPage from "./pages/OrderSummaryPage";
import PaymentPage from "./pages/PaymentPage";
import OrderConfirmationPage from "./pages/OrderConfirmationPage";
import PosProfileSelectionPage from "./pages/PosProfileSelectionPage";

const childRoutes = [
    {
        path: "/",
        element: <WelcomePage/>
    },
    {
        path: "/select-profile",
        element: <PosProfileSelectionPage/>
    },
    {
        path: "/menu",
        element: <MenuBrowsePage/>
    },
    {
        path: "/order-summary",
        element: <OrderSummaryPage/>
    },
    {
        path: "/payment",
        element: <PaymentPage/>
    },
    {
        path: "/order-confirmation/:orderId",
        element: <OrderConfirmationPage/>
    }
];

export const routes = [
    {
        path: "/",
        element: <Layout/>,
        children: childRoutes
    }
]