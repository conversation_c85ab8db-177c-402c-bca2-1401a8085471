import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { ItemPrice } from "../model/item-price.model";

export const useItemPrices = (priceList?: string) => {
    return useQuery({
        queryKey: ["item-prices", priceList],
        queryFn: async () => {
            const params = priceList ? { price_list: priceList } : {};
            const res = await axios.get<ItemPrice[]>("/pricing/item-prices", { params });
            return res.data
        },
        enabled: !!priceList,
    });
}; 