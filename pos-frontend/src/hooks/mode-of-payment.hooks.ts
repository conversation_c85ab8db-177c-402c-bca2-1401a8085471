import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { ModeOfPayment } from "../model/mode-of-payment.model";

export const useModesOfPayment = () => {
  return useQuery({
    queryKey: ["modes-of-payment"],
    queryFn: async () => {
      const response = await axios.get<ModeOfPayment[]>("/modes-of-payment");
      return response.data;
    },
  });
};

export const useModeOfPayment = (name?: string) => {
  return useQuery<ModeOfPayment | null>({
    queryKey: ["mode-of-payment", name],
    queryFn: async (): Promise<ModeOfPayment | null> => {
      try {
        const response = await axios.get<ModeOfPayment>(`/modes-of-payment/${name}`);
        return response.data;
      } catch (error) {
        // Return null for 404 errors (payment method not found)
        if (axios.isAxiosError(error) && error.response?.status === 404) {
          return null;
        }
        throw error;
      }
    },
    enabled: !!name,
  });
};