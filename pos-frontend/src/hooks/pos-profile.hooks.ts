import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { PosProfile, PosPaymentMethod } from "../model/pos-profile.model";

const SELECTED_POS_PROFILE_KEY = "selectedPosProfileId";

export const usePosProfiles = () => {
    return useQuery({
        queryKey: ["pos-profiles"],
        queryFn: async () => {
            const res = await axios.get<PosProfile[]>("/pos-profiles");
            return res.data;
        },
    });
};

// Helper function to get payment methods from POS profile
export const getPaymentMethodsFromProfile = (posProfile: PosProfile): PosPaymentMethod[] => {
  return posProfile.payments || [];
};

// Local storage utilities
export const getSelectedPosProfileId = (): string | null => {
  return localStorage.getItem(SELECTED_POS_PROFILE_KEY);
};

export const setSelectedPosProfileId = (profileId: string): void => {
  localStorage.setItem(SELECTED_POS_PROFILE_KEY, profileId);
};

export const clearSelectedPosProfileId = (): void => {
  localStorage.removeItem(SELECTED_POS_PROFILE_KEY);
};

// Hook to get the selected POS profile with refetch interval
export const useSelectedPosProfile = () => {
  const selectedProfileId = getSelectedPosProfileId();
  
  return useQuery({
    queryKey: ["selected-pos-profile", selectedProfileId],
    queryFn: async () => {
      if (!selectedProfileId) {
        throw new Error('No POS profile selected');
      }
      
      const res = await axios.get<PosProfile>(`/pos-profiles/${selectedProfileId}`);
      return res.data;
    },
    enabled: !!selectedProfileId,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
}; 