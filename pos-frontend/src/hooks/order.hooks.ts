import { useMutation, useQuery } from "@tanstack/react-query";
import axios from "axios";
import { CartItem } from "../stores/cartStore";

export interface CreateOrderPayment {
  amount: number;
  currency: string;
  method: string;
  status: 'Completed';
  transactionReference: string;
}

export interface CreateOrderPayload {
  customer: string;
  items: {
    itemCode: string;
    itemName: string;
    quantity: number;
    unitPrice: number;
    currency: string;
    customizations?: unknown;
    lineTotal: number;
  }[];
  totalAmount: number;
  currency: string;
  posProfile: string;
  payments: CreateOrderPayment[];
}

export interface OrderResponse {
  localId: string;
  displayId: number;
  customer: string;
  totalAmount: number;
  currency: string;
  orderStatus: string; // Will be 'Paid' 
  syncStatus: string;  // Will be 'Pending'
  posProfile: string;
  createdAt: string;
  updatedAt: string;
}

export const useCreateOrder = () => {
  return useMutation({
    mutationFn: async (orderData: CreateOrderPayload): Promise<OrderResponse> => {
      const response = await axios.post<OrderResponse>('/orders', orderData);
      return response.data;
    }
  });
};

export const useOrder = (orderId?: string) => {
  return useQuery({
    queryKey: ["order", orderId],
    queryFn: async () => {
      const response = await axios.get<OrderResponse>(`/orders/${orderId}`);
      return response.data;
    },
    enabled: !!orderId,
  });
};

// Helper function to transform cart data to order payload
export const transformCartToOrderPayload = (
  cartItems: CartItem[],
  total: number,
  payments: CreateOrderPayment[],
  customer: string,
  posProfile: string,
  currency: string
): CreateOrderPayload => {
  return {
    customer,
    items: cartItems.map(cartItem => ({
      itemCode: cartItem.item.name, // Using name as code since it's the primary identifier
      itemName: cartItem.item.name,
      quantity: cartItem.quantity,
      unitPrice: cartItem.priceObj?.price_list_rate || 0,
      currency,
      customizations: cartItem.customizations.length > 0 ? cartItem.customizations : undefined,
      lineTotal: cartItem.subtotal
    })),
    totalAmount: total,
    currency,
    posProfile,
    payments
  };
};