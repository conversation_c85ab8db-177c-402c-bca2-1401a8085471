export const translationsAR = {
  header: "header",
  footer: "footer",
  language: {
    english: "الإنجليزية",
    german: "الألمانية",
    french: "الفرنسية",
    arabic: "العربية",
    label: "اللغات المتاحة",
  },
  theme: {
    label: "المظهر",
    light: "فاتح",
    dark: "داكن",
  },
  api: {
    health: "حالة واجهة برمجة التطبيقات",
    healthy: "متاح",
    unreachable: "غير متاح",
    checking: "جارٍ الفحص...",
    healthyDesc: "واجهة برمجة التطبيقات متاحة وتعمل كما هو متوقع.",
    unreachableDesc: "واجهة برمجة التطبيقات غير متاحة حالياً.",
    checkingDesc: "جارٍ التحقق من حالة واجهة برمجة التطبيقات...",
  },
  welcome: {
    title: "مرحباً بكم في",
    subtitle: "المس الشاشة لبدء طلبك",
    startOrder: "بدء الطلب",
  },
  common: {
    loading: "جارِ التحميل...",
    back: "عودة",
    continue: "متابعة",
    company: "الشركة",
    error_loading_profiles: "خطأ في تحميل الملفات الشخصية",
    no_profiles_available: "لا توجد ملفات شخصية متاحة",
  },
  menu: {
    title: "القائمة",
    categories: "الفئات",
    addToCart: "إضافة إلى السلة",
    noItems: "لم يتم العثور على عناصر",
    selectCategory: "اختر فئة",
    priceNotAvailable: "السعر غير متاح",
    loadingCategories: "جارِ تحميل الفئات...",
    loadingItems: "جارِ تحميل العناصر...",
  },
  cart: {
    title: "سلة التسوق",
    empty: "سلتك فارغة",
    total: "المجموع",
    each: "للواحد",
    remove: "إزالة العنصر",
    item: "عنصر",
    items: "عناصر",
    viewCart: "عرض السلة",
    quantity: "الكمية",
    increaseQuantity: "زيادة الكمية",
    decreaseQuantity: "تقليل الكمية",
  },
  order: {
    summary: "ملخص الطلب",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    total: "المجموع الكلي",
    proceedToPayment: "المتابعة للدفع",
    continueShopping: "متابعة التسوق",
    backToMenu: "العودة للقائمة",
    emptyCartPaymentDisabled: "أضف عناصر إلى السلة قبل المتابعة للدفع",
  },
  payment: {
    title: "الدفع",
    amountDue: "المبلغ المطلوب",
    orderSummary: "ملخص الطلب",
    selectMethod: "اختر طريقة الدفع",
    selectAction: "اختر إجراء الدفع",
    simulateSuccess: "معالجة الدفع (نجاح)",
    simulateFailure: "محاكاة فشل الدفع",
    processing: "جاري معالجة الدفع...",
    pleaseWait: "يرجى الانتظار بينما نعالج دفعتك",
    approved: "تم قبول الدفع",
    declined: "تم رفض الدفع",
    error: "خطأ في الدفع",
    ready: "جاهز للدفع",
    retry: "إعادة محاولة الدفع",
    backToCart: "العودة للسلة",
    redirecting: "جاري التوجيه للتأكيد...",
    paymentMethod: "طريقة الدفع",
    paymentMethods: "طرق الدفع",
    addPayment: "إضافة دفعة",
  },
  confirmation: {
    title: "تأكيد الطلب",
    success: "تم الدفع بنجاح!",
    message: "تم تأكيد طلبك ومعالجة الدفع.",
    orderDetails: "تفاصيل الطلب",
    orderNumber: "رقم الطلب",
    orderTime: "وقت الطلب",
    customer: "العميل",
    total: "الإجمالي",
    paymentMethod: "طريقة الدفع",
    status: "الحالة",
    loading: "جاري تحميل تفاصيل الطلب...",
    error: "خطأ",
    errorMessage: "تعذر تحميل تفاصيل الطلب. يرجى المحاولة مرة أخرى.",
    card: "بطاقة ائتمان",
    paid: "مدفوع",
    whatNext: "ماذا تريد أن تفعل؟",
    printReceipt: "طباعة الإيصال",
    newOrder: "بدء طلب جديد",
    autoRedirect: "سيتم توجيهك إلى الصفحة الرئيسية في ثوانٍ قليلة.",
    thankYou: "شكراً لك!",
    enjoyMeal: "نتمنى أن تستمتع بوجبتك.",
  },
  posProfile: {
    select_pos_profile: "اختيار ملف تعريف نقطة البيع",
    select_pos_profile_description: "يرجى اختيار ملف تعريف نقطة البيع الخاص بك للمتابعة. سيؤدي ذلك إلى تكوين التطبيق وفقاً لملفك الشخصي المخصص.",
  },
};