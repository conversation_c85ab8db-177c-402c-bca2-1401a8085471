import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './i18n/i18n'
import './index.css'
import axios from 'axios'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc)

const queryClient = new QueryClient()

axios.defaults.baseURL = import.meta.env.VITE_API_URL + '/api/v1'
axios.defaults.headers.common['Authorization'] = `Bearer ${import.meta.env.VITE_API_KEY}`

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </StrictMode>,
)
