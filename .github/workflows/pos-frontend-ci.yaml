name: POS Frontend CI Workflow
on:
  workflow_call:
  pull_request:
    branches:
      - main
      - production
    paths:
      - 'pos-frontend/**'
      
concurrency:
  group: pos-frontend-ci-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  install-deps:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 💾 Cache node_modules
        uses: actions/cache@v4
        with:
          path: ./pos-frontend/node_modules
          key: ${{ runner.os }}-pos-frontend-node-modules-${{ hashFiles('pos-frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-pos-frontend-node-modules-

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./pos-frontend/package-lock.json

      - name: 📦 Install dependencies for pos-frontend
        working-directory: ./pos-frontend
        run: npm ci

  lint-check:
    needs: install-deps
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./pos-frontend/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./pos-frontend/node_modules
          key: ${{ runner.os }}-pos-frontend-node-modules-${{ hashFiles('pos-frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-pos-frontend-node-modules-

      - name: 🧹 Run linter
        working-directory: ./pos-frontend
        run: npm run lint

  unit-tests:
    needs: install-deps
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./pos-frontend/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./pos-frontend/node_modules
          key: ${{ runner.os }}-pos-frontend-node-modules-${{ hashFiles('pos-frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-pos-frontend-node-modules-

      - name: 🧪 Run unit tests
        working-directory: ./pos-frontend
        run: npm run test:coverage

  build-electron:
    if: github.event_name == 'pull_request'
    needs: [install-deps, lint-check, unit-tests]
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./pos-frontend/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./pos-frontend/node_modules
          key: ${{ runner.os }}-pos-frontend-node-modules-${{ hashFiles('pos-frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-pos-frontend-node-modules-

      - name: 🍷 Install Wine for Windows cross-compilation
        run: |
          sudo dpkg --add-architecture i386
          sudo apt update
          sudo apt install -y wine64 wine
          wine --version

      - name: ⚡ Build Electron app (Self-Ordering) - Windows  
        working-directory: ./pos-frontend
        run: npm run electron:build:self-ordering
        env:
          USE_HARD_LINKS: false

  build-docker-image:
    if: github.event_name == 'pull_request'
    needs: [install-deps, lint-check, unit-tests]
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: ./pos-frontend/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./pos-frontend/node_modules
          key: ${{ runner.os }}-pos-frontend-node-modules-${{ hashFiles('pos-frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-pos-frontend-node-modules-

      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-pos-frontend
          tags: |
            type=raw,value=pr-${{ github.event.pull_request.number }}

      - name: 🐳 Build Docker image pos-frontend
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./pos-frontend
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 