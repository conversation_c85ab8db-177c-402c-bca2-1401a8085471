name: Kitchen Panel CI Workflow
on:
  workflow_call:
  pull_request:
    branches:
      - main
      - production
    paths:
      - 'kitchen-panel/**'
      
concurrency:
  group: kitchen-panel-ci-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  install-deps:
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 💾 Cache node_modules
        uses: actions/cache@v4
        with:
          path: ./kitchen-panel/node_modules
          key: ${{ runner.os }}-kitchen-panel-node-modules-${{ hashFiles('kitchen-panel/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-kitchen-panel-node-modules-

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./kitchen-panel/package-lock.json

      - name: 📦 Install dependencies for kitchen-panel
        working-directory: ./kitchen-panel
        run: npm ci

  lint-check:
    needs: install-deps
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./kitchen-panel/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./kitchen-panel/node_modules
          key: ${{ runner.os }}-kitchen-panel-node-modules-${{ hashFiles('kitchen-panel/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-kitchen-panel-node-modules-

      - name: 🧹 Run linter
        working-directory: ./kitchen-panel
        run: npm run lint

  build-docker-image:
    if: github.event_name == 'pull_request'
    needs: [install-deps, lint-check]
    runs-on: ubuntu-latest
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v4

      - name: 🧰 Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'
          cache-dependency-path: ./kitchen-panel/package-lock.json

      - name: 💾 Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: ./kitchen-panel/node_modules
          key: ${{ runner.os }}-kitchen-panel-node-modules-${{ hashFiles('kitchen-panel/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-kitchen-panel-node-modules-

      - name: 🔨 Build application for Docker
        working-directory: ./kitchen-panel
        run: npm run build

      - name: 🏷️ Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-kitchen-panel
          tags: |
            type=raw,value=pr-${{ github.event.pull_request.number }}

      - name: 🐳 Build Docker image kitchen-panel
        uses: docker/build-push-action@v6.15.0
        with:
          context: ./kitchen-panel
          push: false
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: prod 